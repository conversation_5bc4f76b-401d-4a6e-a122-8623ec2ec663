<?php
include_once('../codebase/function/func_geramodelos.inc');
include_once('config_modelo.inc');

global $db, $sqlCompleto, $userGt, $_isAjax;
$_isAjax=true;
if (!$db) $db=getDb();
$sanitiza=true;
if ($sanitiza) cleanRequest();
$method=$_REQUEST['m'];

if (function_exists($method)){
	call_user_func($method);
}
function getTables(){
	$modelFile = $_REQUEST['file'];
	$folder=dirname($modelFile);
	// echo $folder;exit;
	$modelContent = trim(file_get_contents($modelFile, true));
	// get modelFile folder
	// echo $modelContent;exit;
	$positionsFile = str_replace('.json', '.pos.json', $modelFile);
	$positions=null;
	if (file_exists($positionsFile)) {
		$positionsContent = trim(file_get_contents($positionsFile, true));
		$positions = json_decode($positionsContent, true);
	}
	
	// Check if the file is already in JSON format (like gtdm1.json)
	$firstChar = substr(trim($modelContent), 0, 1);
	if ($firstChar == '{' || $firstChar == '[') {
		// It's already JSON, no need to clean
		if ($positions) {
			$model = json_decode($modelContent, true);
			$model['positions'] = $positions;
			echo json_encode($model, JSON_PRETTY_PRINT);
			exit;
		}
		echo $modelContent;
		exit;
	} else {
		// Handle the old format (var tbDefs=...)
		$modelLimpo = str_replace('var tbDefs=', '', $modelContent);
		if (substr($modelLimpo, -1) == ';') $modelLimpo = substr($modelLimpo, 0, -1);
		if ($positions) {
			$model = json_decode($modelLimpo, true);
			$model['positions'] = $positions;
			echo json_encode($model, JSON_PRETTY_PRINT);
			exit;
		}
		echo $modelLimpo;
		exit;
	}
}
function getPositions(){
	$positionsFile = $_REQUEST['file'];
	$positionsFile = str_replace('.json', '.pos.json', $positionsFile);
	
	if (!file_exists($positionsFile)) {
		// If positions file doesn't exist, create an empty one
		echo '{}';
		exit;
	}
	
	$positionsContent = trim(file_get_contents($positionsFile, true));
	
	// Check if the file is already in JSON format
	$firstChar = substr(trim($positionsContent), 0, 1);
	if ($firstChar == '{' || $firstChar == '[') {
		// It's already JSON, no need to clean
		echo $positionsContent;
		exit;
	} else {
		// Handle the old format (var tbPositions=...)
		$positionsLimpo = str_replace('var tbPositions=', '', $positionsContent);
		if (substr($positionsLimpo, -1) == ';') $positionsLimpo = substr($positionsLimpo, 0, -1);
		echo $positionsLimpo;
		exit;
	}
}
function savePositions(){
	global $db_gt, $sqlCompleto;
	header('Content-type:text/javascript; charset=UTF-8;');
	$positions = $_REQUEST['positions'];
	$modelFile = $_REQUEST['file'];
	$positionsFile = str_replace('.json', '.pos.json', $modelFile);
	file_put_contents($positionsFile, $positions, FILE_USE_INCLUDE_PATH);
	echo 'showStatus("Layout salvo com sucesso!");';
}
function transforma($tbDefs){
	$pks=[];
	foreach($tbDefs as $table=>$defs){
		foreach($defs['columns'] as $col=>$colDef){
			if ($colDef['pk']) $pks[$table][]=$col;
		}
	}
	foreach($tbDefs as $table=>$defs){
		$fks=[];
		foreach($defs['columns'] as $col=>$colDef){
			if (isset($colDef['fk'])){
				$key=$colDef['fk']['table'].'.'.($colDef['phrase']?$colDef['phrase']:$colDef['fk']['column']).'.'.($colDef['inverse']?$colDef['inverse']:$col);
				if (!isset($fks[$key])) $fks[$key]=['ref'=>$colDef['fk']['table'],'columns'=>[],'phrase'=>($colDef['phrase']?$colDef['phrase']:null),'inverse'=>($colDef['inverse']?$colDef['inverse']:null)];
				$fks[$key]['columns'][$col]=$colDef['fk']['column'];
			}
		}
		if ($table=='TB0_LSRP'){
			echo nl2br(print_r($fks, true))."<br/>";
			// exit;
		}
		$fkDef=[];
		foreach($fks as $key=>$fk){
			$ehPk=true;
			$ehMand=true;
			foreach($fk['columns'] as $col=>$refCol){
				if ($defs['columns'][$col]['pk']!=true) $ehPk=false;
				if ($defs['columns'][$col]['nullable']==true) $ehMand=false;
			}
			$pkCols=$pks[$fk['ref']];
			foreach($pkCols as $col){
				if (!in_array($col, $fk['columns'])) $fk['columns'][$col]=$col;
			}
			ksort($fk['columns']);
			$name=$table.'_'.implode('_', array_keys($fk['columns'])).'_FK';
			$fkDef[$name]=['ref'=>$fk['ref'],'columns'=>[]];
			foreach($fk['columns'] as $col=>$refCol){
				$fkDef[$name]['columns'][]=['col'=>$col, 'ref'=>$refCol];
			}
			if ($fk['phrase']) $fkDef[$name]['phrase']=$fk['phrase'];
			if ($fk['inverse']) $fkDef[$name]['inverse']=$fk['inverse'];
			if ($ehPk) {
				$fkDef[$name]['reltype']='id';
			}elseif (!$ehPk && $ehMand) {
				$fkDef[$name]['reltype']='nonidmand';
			}else{
				$fkDef[$name]['reltype']='nonidopt';
			}

			unset($fks[$key]);
		}
		$tbDefs[$table]['parents']=$fkDef;
		if ($table=='TB0_LSRP'){
			echo nl2br(print_r($fkDef, true))."<br/>";
			// exit;
		}
	}
	exit;
	return $tbDefs;
}
function saveModel(){
	global $db_gt, $sqlCompleto;
	header('Content-type:text/javascript; charset=UTF-8;');
	$modelFile=$_REQUEST['file'];
	$tbDefs=json_decode($_REQUEST['tbDefs'], true);
	$modelDefs=json_decode($_REQUEST['modelDefs'], true);
	$redir=$_REQUEST['redir'] ?? null;

	$final=($_REQUEST['final']=='true');
	// $tbDefs=transforma($tbDefs);
	ksort($tbDefs);
	if ($final) {
		copy($modelFile, $modelFile.'.bak');
		ksort($tbDefs['entities']);
		foreach($tbDefs['entities'] as $tb=>$tbDef){
			// ksort($tbDef['columns']);
			foreach($tbDef['columns'] as $col=>$colDef){
				if (isset($colDef['sequence']) && $colDef['sequence']==false) unset($tbDefs['entities'][$tb]['columns'][$col]['sequence']);
				if (isset($colDef['default']) && $colDef['default']=='') unset($tbDefs['entities'][$tb]['columns'][$col]['default']);
			}
		}
		$json=json_encode(['entities'=>$tbDefs['entities'], 'relationships'=>$tbDefs['relationships'], 'modelDefs'=>$modelDefs], JSON_PRETTY_PRINT);
		if (file_put_contents($modelFile, $json, FILE_USE_INCLUDE_PATH)){
			echo 'showStatus("Modelo salvo com sucesso!");'.(!empty($redir)?"abreGera();" : '');
		}else{
			echo 'showStatus("Erro ao salvar modelo!", "lightcoral");';
		}
	}else{
		$file=$modelFile.'.tmp';
		$json=json_encode(['entities'=>$tbDefs['entities'], 'relationships'=>$tbDefs['relationships'], 'modelDefs'=>$modelDefs], JSON_PRETTY_PRINT);
		file_put_contents($file, $json, FILE_USE_INCLUDE_PATH);
	}
}
function editTable(){
	global $db_gt, $sqlCompleto;
	$tableId=$_REQUEST['tableId'];
	$modelFile=$_REQUEST['file'];
	$modelContent=trim(file_get_contents($modelFile, true));
	$modelLimpo=str_replace('var tbDefs=', '', $modelContent);
	if (substr($modelLimpo, -1)==';') $modelLimpo=substr($modelLimpo, 0, -1);
	// echo nl2br(print_r(str_replace('var tbDefs=', '', $modelLimpo), true))."<br/>";exit;
	$tbDefs=json_decode($modelLimpo,true);
	// ksort($tbDefs);
	// $json=json_encode($tbDefs, JSON_PRETTY_PRINT);
	// file_put_contents($modelFile, $json, FILE_USE_INCLUDE_PATH);
	$tbDef=$tbDefs[$tableId];
	// echo nl2br(print_r($tbDef['constraints'], true))."<br/>";
	$numUniques=0;
	if (isset($tbDef['constraints'])){
		foreach($tbDef['constraints'] as $ctName=>$ctDef){
			if ($ctDef['type']=='unique') $numUniques++;
		}
	}
?>
<div class="content container-fluid" style="font-size:0.9em;max-height:90vh;overflow-y:auto;">
	<div class="row">
		<div class="col-sm-5">
			<div class="form-group-sm">
				<label for="tableId">Nome Físico</label>
				<input type="text" class="form-control" name="tableId" id="tableId" value="__tableId__"/>
			</div>
		</div>
		<div class="col-sm-5">
			<div class="form-group-sm">
				<label for="logicalName">Nome Lógico</label>
				<input type="text" class="form-control" name="logicalName" id="logicalName" value="__logicalName__"/>
			</div>
		</div>
		<div class="col-sm-2" style="text-align: right;vertical-align: bottom;">
			<div class="form-group-sm">
				<label>&nbsp;</label><br/>
				<button type="button" id="butUpdateTableNames" class="btn btn-default btn-sm" onclick="updateTable('__tableId__');">Atualizar</button>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<b>Colunas</b>
			<div id="columnsContainer" style="max-height:350px;overflow-y:auto;">
				<table class="table table-condensed table-hover table-striped">
					<thead>
						<tr>
							<th>&nbsp;</th>
							<th>Nome</th>
							<th>Nome Lógico</th>
							<th>Tipo</th>
							<th>PK/Null</th>
							<th>Def</th>
							<th><i class="fa fa-trash-o" style="color:gray;"></i></th>
						</tr>
					</thead>
					<tbody>
						<tr ondblclick="editColumn('__tableId__', '__columnId__');" id="templateColumnTr">
							<td>__ico__</td>
							<td>__columnId__</td>
							<td>__colLogicalName__</td>
							<td>__type</td>
							<td>__pknull__</td>
							<td>__default__</td>
							<td><i class="fa fa-trash-o simulink" onclick="removeColumn('__tableId__', '__columnId__');"></i></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="row" style="margin-top: 5px;">
		<div class="col-sm-4 text-center">
			<button type="button" class="btn btn-default btn-sm" onclick="addColumn('__tableId__',($('#logicalName').val()!=''?$('#logicalName').val():null));">Adicionar Coluna</button>
		</div>
		<div class="col-sm-4 text-center">
			<button type="button" class="btn btn-default btn-sm" onclick="editUniques('__tableId__');">Chaves únicas (__numUniques__)</button>
		</div>
		<div class="col-sm-4 text-center">
			<button type="button" class="btn btn-default btn-sm" onclick="removeTable('__tableId__');">Remover Tabela</button>
		</div>
	</div>
</div>
<?
}
function editColumn(){
	global $db_gt, $sqlCompleto, $_configModeloTypes;
	$tableId=$_REQUEST['tableId'];
	$columnId=$_REQUEST['columnId'];
	$modelFile=$_REQUEST['file'];
	$modelContent=trim(file_get_contents($modelFile, true));
	$modelLimpo=str_replace('var tbDefs=', '', $modelContent);
	if (substr($modelLimpo, -1)==';') $modelLimpo=substr($modelLimpo, 0, -1);
	// echo nl2br(print_r(str_replace('var tbDefs=', '', $modelLimpo), true))."<br/>";exit;
	$modelDefs=json_decode($modelLimpo,true);
	$tbDefs=$modelDefs['entities'] ?? $modelDefs;
	// echo nl2br(print_r($tbDefs[$tableId], true))."<br/>";
	$tbDef=$tbDefs[$tableId];
	// echo nl2br(print_r($tbDef['columns'][$columnId], true))."<br/>";
	// $colDef=$tbDef['columns'][$columnId];
	$types=$_configModeloTypes;
	$defsRel='';
?>
<div class="content container-fluid" style="font-size:0.9em;">
	<div class="row">
		<div class="col-sm-12">
			Tabela <b>__tableLogicalName__</b> (__tableId__) - Coluna <b>__colLogicalName__</b> (__columnId__)
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<div class="form-group">
				<label for="columnId">Nome Físico</label>
				<input type="text" class="form-control" name="columnId" id="columnId" value="__columnId__"/>
			</div>
		</div>
		<div class="col-sm-6">
			<div class="form-group">
				<label for="colLogicalName">Nome Lógico</label>
				<input type="text" class="form-control" name="colLogicalName" id="colLogicalName" value="__colLogicalName__"/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<div class="form-group">
				<label id="typeLabel">Tipo</label>
				<select class="form-control" name="type" id="type">
					<option value="">Selecione</option>
<?
					foreach($types as $type=>$props){
?>
						<option id="type_<?=$type?>" value="<?=$type?>"<?=($props['size']?' temSize="true"':'')?>><?=$type?></option>
<?
					}
?>
				</select>
			</div>
		</div>
		<div class="col-sm-6" id="divSize" style="display:none;">
			<div class="form-group-sm">
				<label for="size">Tamanho</label>
				<input type="text" class="form-control" name="size" id="size" value="__size__"/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<b>Características</b>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="pk" id="pk" value="true"/> Primary Key
				</label>
			</div>
		</div>
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="sequence" id="sequence" value="true"/> Sequência
				</label>
			</div>
		</div>
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="notnull" id="notnull" value="true"/> Not null
				</label>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<div class="form-group">
				<label for="columnDefault">Valor default</label>
				<input type="text" class="form-control" name="columnDefault" id="columnDefault" value="__columnDefault__"/>
			</div>
		</div>
	</div>
	<div id="divRelacionamento" class="row" style="border-top:solid 1px lightgray;padding-top:10px;display:none;">
		<div class="col-sm-12">
			Relacionamento: __relacionamento__
		</div>
		<div class="col-sm-6">
			<div class="form-group">
				<label for="phrase">Frase direta</label>
				<input type="text" class="form-control" name="phrase" id="phrase" value="__phrase__"/>
			</div>
		</div>
		<div class="col-sm-6">
			<div class="form-group">
				<label for="inverse">Inversa</label>
				<input type="text" class="form-control" name="inverse" id="inverse" value="__inverse__"/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<button type="button" class="btn btn-default btn-sm" onclick="$('#editColumnDialog').dialog('close');">Fechar</button>
		</div>
		<div class="col-sm-6 text-right">
			<button type="button" class="btn btn-primary btn-sm" onclick="updateColumn('__tableId__', '__columnId__');">Atualizar</button>
		</div>
	</div>

</div>
<?
}
function addColumn(){
	global $db_gt, $sqlCompleto, $_configModeloTypes;
	$tableId=$_REQUEST['tableId'];
	$modelFile=$_REQUEST['file'];
	$modelContent=trim(file_get_contents($modelFile, true));
	$modelLimpo=str_replace('var tbDefs=', '', $modelContent);
	if (substr($modelLimpo, -1)==';') $modelLimpo=substr($modelLimpo, 0, -1);
	// echo nl2br(print_r(str_replace('var tbDefs=', '', $modelLimpo), true))."<br/>";exit;
	$modelDefs=json_decode($modelLimpo,true);
	$tbDefs=$modelDefs['entities'] ?? $modelDefs;
	$tbDef=$tbDefs[$tableId] ?? null;
	$logicalName=$tbDef['logical'] ?? 'NOVA TABELA';
	if (!empty($_REQUEST['logical'])) $logicalName=$_REQUEST['logical'];
	$types=$_configModeloTypes;
?>
<div class="content container-fluid" style="font-size:0.9em;">
	<div class="row">
		<div class="col-sm-12">
			Tabela <b><?=$logicalName?></b> (<?=$tableId?>)
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<div class="form-group">
				<label for="columnId">Nome Físico</label>
				<input type="text" class="form-control" name="columnId" id="columnId" value=""/>
			</div>
		</div>
		<div class="col-sm-6">
			<div class="form-group">
				<label for="colLogicalName">Nome Lógico</label>
				<input type="text" class="form-control" name="colLogicalName" id="colLogicalName" value=""/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<div class="form-group">
				<label>Tipo</label>
				<select class="form-control" name="type" id="type" onchange="if ($(this).find(':selected').attr('temSize')=='true') $('#divSize').show(); else $('#divSize').hide();">
					<option value="">Selecione</option>
<?
					foreach($types as $type=>$props){
?>
						<option value="<?=$type?>" id="type_<?=$type?>"<?=(isset($props['size'])?' temSize="true"':'')?>><?=$type?></option>
<?
					}
?>
				</select>
			</div>
		</div>
		<div class="col-sm-6" id="divSize" style="display:none;">
			<div class="form-group-sm">
				<label for="size">Tamanho</label>
				<input type="number" class="form-control" name="size" id="size" value=""/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<b>Características</b>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="pk" id="pk" value="true" /> Primary Key
				</label>
			</div>
		</div>
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="sequence" id="sequence" value="true" /> Sequência
				</label>
			</div>
		</div>
		<div class="col-sm-4">
			<div class="checkbox">
				<label>
					<input type="checkbox" name="notnull" id="notnull" value="true" /> Not null
				</label>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<button type="button" class="btn btn-default btn-sm" onclick="$('#addColumnDialog').dialog('close');">Fechar</button>
		</div>
		<div class="col-sm-6 text-right">
			<button type="button" class="btn btn-primary btn-sm" onclick="addColumn_go('<?=$tableId?>');">Adicionar Coluna</button>
		</div>
	</div>
</div>
<?
}
function getuniqueListDialogHtml(){
	global $db_gt, $sqlCompleto;
	$tableId=$_REQUEST['tableId'];
	$modelFile=$_REQUEST['file'];
?>
<div class="content container-fluid" style="font-size:0.9em;">
	<div class="row">
		<div class="col-sm-12">
			Tabela <b>__tableLogicalName__</b> (__tableId__)
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<b>Chaves únicas</b>
			<div id="uniquesContainer" style="max-height:350px;overflow-y:auto;">
				<table class="table table-condensed table-hover table-striped">
					<thead>
						<tr>
							<th>Nome</th>
							<th>Colunas</th>
							<th><i class="fa fa-trash-o" style="color:gray;"></th>
						</tr>
					</thead>
					<tbody>
						<tr id="templateUniqueTr" style="display:none;" ondblclick="editUnique('__tableId__', this.id.replace('uniqueTr_', ''));">
							<td>__uniqueName__</td>
							<td>__uniqueColumns__</td>
							<td><i class="fa fa-trash-o simulink" onclick="removeUnique('__tableId__', $(this).closest('tr').attr('id').replace('uniqueTr_', ''));"></i></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-6">
			<button type="button" class="btn btn-default btn-sm" onclick="$('#uniqueListDialog').dialog('close');">Fechar</button>
		</div>
		<div class="col-sm-6" style="text-align:right;">
			<button type="button" class="btn btn-primary btn-sm" onclick="addUnique('__tableId__');">Adicionar Chave Única</button>
		</div>
	</div>
</div>
<?
}
function getUniqueDialog(){
	global $db_gt, $sqlCompleto;
	$modelFile=$_REQUEST['file'];
	?>
	<div class="content container-fluid" style="font-size:0.9em;">
		<div class="row">
			<div class="col-sm-12">
				Tabela <b>__tableLogicalName__</b> (__tableId__)
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<div class="form-group-sm">
					<label for="uniqueKeyName">Nome da Chave Única</label>
					<input type="text" class="form-control" name="uniqueKeyName" id="uniqueKeyName" value=""/>
					<span id="uniqueKeyNameSuggested" style="color:gray;font-size:0.8em;"></span>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<b>Colunas</b>
				<div id="uniqueColumnsContainer" style="max-height:350px;overflow-y:auto;">
					<table class="table table-condensed table-hover table-striped">
						<thead>
							<tr>
								<th>&nbsp;</th>
								<th>Nome</th>
								<th>Nome Lógico</th>
								<th>Tipo</th>
							</tr>
						</thead>
						<tbody>
							<tr id="templateUniqueTrCols" style="display:none;">
								<td><input type="checkbox" name="col" id="cb-__columnId__" value="true" onchange="updateNameUnique('__tableId__');"/></td>
								<td>__columnId__</td>
								<td>__columnLogical__</td>
								<td>__columnDefs__</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-6">
				<button type="button" class="btn btn-default btn-sm" onclick="$('#uniqueDialog').dialog('close');">Fechar</button>
			</div>
			<div class="col-sm-6 text-right">
				<button type="button" class="btn btn-primary btn-sm" onclick="__btnUniqueClick__">__btnUniqueText__</button>
			</div>
		</div>
	</div>
	<?
}








function getRelationshipDialog(){
	global $db_gt, $sqlCompleto;
	$modelFile=$_REQUEST['file'];
	$modelContent=trim(file_get_contents($modelFile, true));
	$modelLimpo=str_replace('var tbDefs=', '', $modelContent);
	if (substr($modelLimpo, -1)==';') $modelLimpo=substr($modelLimpo, 0, -1);
	// echo nl2br(print_r(str_replace('var tbDefs=', '', $modelLimpo), true))."<br/>";exit;
	$tbDefs=json_decode($modelLimpo,true);
?>
<div class="content container-fluid" style="font-size:0.9em;margin-bottom:5px;" id="relationshipForms">
	<div id="relDivsWrapper" class="container-fluid" style="max-height: 650px;overflow-y: auto;">
		<div id="portaDivUmaRel">
			<div id="portaRelToCols" style="padding-top: 5px;border-bottom: solid 1px lightgray;margin-bottom: 5px;">
				<div class="row">
					<div class="col-sm-12" style="margin-bottom: 5px;">
						Parent: <b>__relfromLogical__</b> (__relfrom__) =&gt; Child: <b>__reltoLogical__</b> (__relto__)
					</div>
					<div class="col-sm-12">
						<div class="form-group-sm">
							<label for="relName">Nome</label>
							<input type="text" class="form-control" name="relName" id="relName" value="__relName__"/>
						</div>
					</div>
				</div>
				<div id="divRelToCols" style="display:none;">
					<div class="row">
						<div class="col-sm-12 grayed-bg">
							Parent column: <b>__relcolfromLogical__</b> (__relcolFromPhysical__: __relFromColDescr__) 
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group-sm">
								<label for="tocolLogical">Lógico</label>
								<input type="text" class="form-control relcollogical" name="tocolLogical" id="tocolLogical" value="__tocolLogical__"/>
							</div>
						</div>
						<div class="col-sm-6">
							<div class="form-group-sm">
								<label for="tocolPhysical">Físico</label>
								<input type="text" class="form-control relcolphysical" name="tocolPhysical" id="tocolPhysical" value="__tocolPhysical__" onkeyup="updateRelName('__relfrom__','__relto__');" onchange="updateRelName('__relfrom__','__relto__');"/>
								<div id="distinctiveMsg" style="color:gray;font-size:0.8em;display:none;"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group-sm">
						<label for="reltype">Tipo</label>
						<select class="form-control" name="reltype" id="reltype">
							<option value="0">Incluir na PK</option>
							<option value="1">Não incluir na PK, obrigatória</option>
							<option value="2">Não especificada</option>
							<option value="3">Não incluir na PK, opcional</option>
						</select>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group-sm">
						<label for="cardinality">Cardinalidade</label>
						<select class="form-control" name="cardinality" id="cardinality">
							<option class="mandatory" value="0m">1 para 0 ou mais</option>
							<option class="mandatory" value="1m">1 para 1 ou mais</option>
							<option class="mandatory" value="2m">1 para 0 ou 1</option>
							<option class="mandatory" value="3m">1 para N</option>
							<option class="optional" value="0o">0 ou 1 para 0 ou mais</option>
							<option class="optional" value="1o">0 ou 1 para 1 ou mais</option>
							<option class="optional" value="2o">0 ou 1 para 0 ou 1</option>
							<option class="optional" value="3o">0 ou 1 para N</option>
						</select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group-sm">
						<label for="phrase">Frase direta</label>
						<input type="text" class="form-control" name="phrase" id="newrelphrase" value="__phrase__"/>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group-sm">
						<label for="inverse">Inversa</label>
						<input type="text" class="form-control" name="inverse" id="newrelinverse" value="__inverse__"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-4">
					<button type="button" class="btn btn-default btn-sm" onclick="$('#relationshipDialog').dialog('close');">Fechar</button>
				</div>
				<div class="col-sm-8 text-right">
					<button id="btnDeleteRel" type="button" class="btn btn-default btn-sm" onclick="__btnDeleteRelClick__" style="display:none;">Excluir Relacionamento</button>
					<button id="btnCreateRel" type="button" class="btn btn-primary btn-sm" onclick="__btnCreateRelClick__">Criar Relacionamento</button>
				</div>
			</div>
		</div>
	</div>
</div>
<?
}
function reorderModel() {
    global $db_gt, $sqlCompleto;
    header('Content-type:text/javascript; charset=UTF-8;');
    
    $modelFile = $_REQUEST['file'];
    $type = $_REQUEST['type'] ?? 'all'; // 'entities', 'relationships', or 'all'
    
    if (!file_exists($modelFile)) {
        echo 'showStatus("Error: File not found!", "red");';
        exit;
    }
    
    // Create backup of the original file
    $backupFile = $modelFile . '.bak';
    if (!copy($modelFile, $backupFile)) {
        echo 'showStatus("Warning: Could not create backup file!", "orange");';
        // Continue with the process even if backup fails
    }
    
    $modelContent = trim(file_get_contents($modelFile, true));
    $model = json_decode($modelContent, true);
    
    if ($model === null) {
        echo 'showStatus("Error: Invalid JSON in file!", "red");';
        exit;
    }
    
    // Reorder entities if requested
    if ($type == 'entities' || $type == 'all') {
        // Check if we have the new format with 'entities' property
        if (isset($model['entities'])) {
            $entities = $model['entities'];
            ksort($entities);
            $model['entities'] = $entities;
        } else {
            // Old format where the model itself is the entities object
            ksort($model);
        }
    }
    
    // Reorder relationships if requested
    if ($type == 'relationships' || $type == 'all') {
        // Process entities to reorder their columns and constraints
        $entities = isset($model['entities']) ? $model['entities'] : $model;
        
        foreach ($entities as $tableName => &$table) {
            // Reorder columns
            if (isset($table['columns'])) {
                ksort($table['columns']);
            }
            
            // Reorder constraints
            if (isset($table['constraints'])) {
                ksort($table['constraints']);
            }
            
            // Reorder parents (foreign keys)
            if (isset($table['parents'])) {
                ksort($table['parents']);
            }
        }
        
        // Update the model
        if (isset($model['entities'])) {
            $model['entities'] = $entities;
        } else {
            $model = $entities;
        }
    }
    
    // Save the modified JSON back to the file
    $jsonContent = json_encode($model, JSON_PRETTY_PRINT);
    if (file_put_contents($modelFile, $jsonContent)) {
        echo 'showStatus("Model reordered successfully! Backup saved as ' . basename($backupFile) . '");';
    } else {
        echo 'showStatus("Error saving model file!", "red");';
    }
    exit;
}
function getSaveModelDialogHtml(){
	global $db_gt, $sqlCompleto;
	$modelFile=$_REQUEST['file'];
	?>
	<div class="content container-fluid" style="font-size:0.9em;">
		<div class="row">
			<div class="col-sm-12">
				<h3>Salvar modelo <?=basename($modelFile)?></h3>
				__txtRedir__
			</div>
		</div>
		<div class="row">
			<div class="col-sm-4">
				<div class="form-group-sm">
					<label for="saveModelName">Nome do arquivo</label>
					<input type="text" class="form-control" name="saveModelName" id="saveModelName" value="<?=basename($modelFile)?>"/>
				</div>
			</div>
			<div class="col-sm-4">
				<div class="form-group-sm">
					<label for="saveModelSuffix">Sufixo</label>
					<input type="text" class="form-control" name="saveModelSuffix" id="saveModelSuffix" value="__modelSuffix__"/>
				</div>
			</div>
			<div class="col-sm-4">
				<div class="form-group-sm">
					<label for="saveModelSchema">Schema</label>
					<input type="text" class="form-control" name="saveModelSchema" id="saveModelSchema" value="__modelSchema__"/>
				</div>
			</div>
		</div>
		<div class="row" style="border-top:solid 1px lightgray;padding-top:10px;margin-top: 10px;">
			<div class="col-sm-6">
				<button type="button" class="btn btn-default btn-sm" onclick="$('#saveModelDialog').dialog('close');">Fechar</button>
			</div>
			<div class="col-sm-6 text-right">
				<button type="button" class="btn btn-primary btn-sm" onclick="doSaveModel(__redir__);">Salvar</button>
			</div>
		</div>
	</div>
	<?
}




?>
