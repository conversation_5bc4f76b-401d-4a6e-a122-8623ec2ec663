var modelContainer;
var selectedTable = [];
var draggingTable = null;
var creatingTable = null;
var showing={'list':'logical', 'model':'physical'};
var mmTbs={};
var maiorLeft = null;
var maiorTop = null;
var maiorBottom = null;
var minimapwidth;
var minimapprop;
var modelSize={w:0, h:0};
var draggingState='N'; // N - None, W - Waiting, D - Temp fk lines drawn
var tbPositions=null;
var usaPositions=true;
var tbDefs;
var modelDefs={};
var relats;
// Add variables for marquee selection
var isMarqueeActive = false;
var marqueeStartX = 0;
var marqueeStartY = 0;
var marqueeElement = null;
const delayHover=500;
var hoverTimeoutId;
var defaultModelMargin=300;
// Add variables for right mouse drag
var isRightMouseDragging = false;
var rightDragStartX = 0;
var rightDragStartY = 0;
var rightDragStartScrollLeft = 0;
var rightDragStartScrollTop = 0;
var rightDragOccurred = false;
var preventContextMenu = false;
// Change tracking variables
var hasLayoutChanges = false;
var hasModelChanges = false;
var initialLayoutState = null;
var initialModelState = null;

$(window).on('load',function() {
	if (window.resize) resize();
	loadTables();
	modelContainer = document.getElementById('divModelo');
	$(modelContainer).on('click', function(e) {
		if (isMarqueeActive) return;
		if (creatingTable) {
			creatingTable=false;
			return doNewTable(e);
		}
		if (selectedTable.length) {
			// debugger;
			$('.selected').removeClass('selected');
			selectedTable = [];
			$('#infotable').html('');
		}
	});
	$(modelContainer).on('dblclick', function(e) {
		hideTooltip();
		clickToSelFk(e);
	});
	$(modelContainer).on('mousemove', function(e) {
		if (draggingState=='D') return;
		if (hoverTimeoutId) {
			hideTooltip();
			clearTimeout(hoverTimeoutId);
		}
		if (e.originalEvent.target.id!='divModelo') return;
		hoverTimeoutId = setTimeout(function() {
			// Code to execute after delay
			clickToSelFk(e,true);
		}, delayHover);
	});
	$(modelContainer).on('mouseout', function(e) {
		if (hoverTimeoutId) {
			hideTooltip();
			clearTimeout(hoverTimeoutId);
		}
	});

	// Add right mouse drag functionality
	$(modelContainer).on('mousedown', function(e) {
		if (e.which === 3) { // Right mouse button
			e.preventDefault();
			e.stopPropagation();
			startRightMouseDrag(e);
		}
	});

	// Add cursor indication for right mouse drag
	$(modelContainer).on('mouseenter', function() {
		if (!isRightMouseDragging) {
			// $(this).css('cursor', 'grab');
		}
	});

	$(modelContainer).on('mouseleave', function() {
		if (!isRightMouseDragging) {
			$(this).css('cursor', '');
		}
	});

	$(document).on('mousemove', function(e) {
		if (isRightMouseDragging) {
			e.preventDefault();
			performRightMouseDrag(e);
		}
	});

	$(document).on('mouseup', function(e) {
		if (e.which === 3 && isRightMouseDragging) { // Right mouse button
			e.preventDefault();
			stopRightMouseDrag();
		}
	});

	// Disable context menu on right click to prevent interference
	$(modelContainer).on('contextmenu', function(e) {
		if (isRightMouseDragging || preventContextMenu) {
			e.preventDefault();
			return false;
		}
	});

	// Create canvas for FK lines
	createFkCanvas();
	
	// Initialize info display with zoom level
	$('#infozoom').html('Zoom: 100%');
	
	// Add mouse move event to show coordinates with zoom level
	$('#divModelo').on('mousemove', function(e){
		var x = (e.pageX - $(this).offset().left)/currentScale;
		var y = (e.pageY - $(this).offset().top)/currentScale;
		$('#infopos').html('X: ' + Math.round(x) + ' Y: ' + Math.round(y));
	});
	
	// Add enhanced mouse wheel functionality
	$('#modelo-wrapper').on('wheel', handleScrollEvent);

	minimapwidth=$('#minimap-wrapper').width();
	minimapprop=minimapwidth/modelSize.w;

	$(window).on('resize', function() {
		modResize();
		// Use setTimeout to ensure container has finished resizing
		setTimeout(function() {
			// Ensure container is at least as large as its parent
			var parentWidth = $('#mapWrapper').width();
			var parentHeight = $('#mapWrapper').height();
			var currentWidth = $('#modelo-wrapper').width();
			var currentHeight = $('#modelo-wrapper').height();
			
			if (currentWidth < parentWidth || currentHeight < parentHeight) {
				$('#modelo-wrapper').css({
					'width': (Math.max(currentWidth, parentWidth)+200) + 'px',
					'height': (Math.max(currentHeight, parentHeight)+200) + 'px'
				});
			}
			
			// resizeFkCanvas();
			drawFkLines();
			// updateInfoDisplay();
		}, 100);
	});

	// Initialize marquee selection
	initMarqueeSelection();

	// Initialize change tracking after everything is loaded
	setTimeout(function() {
		initializeChangeTracking();
	}, 1000);

	// Add scroll event listener to track layout changes
	$('#modelo-wrapper').on('scroll', function() {
		// Debounce the change check to avoid excessive calls
		clearTimeout(window.scrollChangeTimeout);
		window.scrollChangeTimeout = setTimeout(function() {
			checkForLayoutChanges();
		}, 100);
	});
	// Add event listener for custom copy action
	document.addEventListener('copy', (event) => {
		if (selectedTable.length==0) return;

		const selection = window.getSelection();
		if (selection.toString().length>0) return;
		event.preventDefault();
		console.log("Original selection: " + selection.toString());
		var selTbs={};
		for (var k=0; k<selectedTable.length; k++){
			selTbs[selectedTable[k]]=tbDefs[selectedTable[k]];
			var left = Math.round(parseInt($('#'+selectedTable[k]).css('left')));
			var top = Math.round(parseInt($('#'+selectedTable[k]).css('top')));
			selTbs[selectedTable[k]].position = [left, top];
		}
		var copyObj={
			"entities": selTbs
		};
		event.clipboardData.setData('text/plain', JSON.stringify(copyObj));
		console.log("Tabelas copiadas para JSON!");
	});
	// Add event listener for custom paste action
	document.addEventListener('paste', (event) => {
		const pastedText = event.clipboardData.getData('text/plain');
		var textIsJson = false;
		try {
			var pasteObj = JSON.parse(pastedText);
			if (pasteObj.entities) {
				textIsJson = true;
			}
			
		} catch (e) {
			console.log("Erro colando JSON: " + e.message);
		}
		if (!textIsJson) {
			return;
		}
		event.preventDefault();
		var existentes=[];
		var novas=[];
		for (var tb in pasteObj.entities) {
			if (!tbDefs[tb]) {
				tbDefs[tb] = pasteObj.entities[tb];
				novas.push(tb);
				drawTable(tb, tbDefs[tb]);
			}else{
				existentes.push(tb);
			}
		}
		drawFkLines();
		console.log("Tabelas coladas do JSON!");
	});
});

// Right mouse drag functions
function startRightMouseDrag(e) {
	isRightMouseDragging = true;
	rightDragOccurred = false;
	preventContextMenu = false;
	rightDragStartX = e.clientX;
	rightDragStartY = e.clientY;
	rightDragStartScrollLeft = $('#modelo-wrapper').scrollLeft();
	rightDragStartScrollTop = $('#modelo-wrapper').scrollTop();

	// Change cursor to indicate dragging
	$('#modelo-wrapper').css('cursor', 'grabbing');
	$('#divModelo').css('cursor', 'grabbing');

	// Prevent text selection during drag
	$('body').css({
		'-webkit-user-select': 'none',
		'-moz-user-select': 'none',
		'-ms-user-select': 'none',
		'user-select': 'none'
	});
}

function performRightMouseDrag(e) {
	if (!isRightMouseDragging) return;

	// Calculate the distance moved
	var deltaX = e.clientX - rightDragStartX;
	var deltaY = e.clientY - rightDragStartY;

	// Check if significant movement occurred (threshold to distinguish from click)
	var dragThreshold = 3; // pixels
	if (Math.abs(deltaX) > dragThreshold || Math.abs(deltaY) > dragThreshold) {
		rightDragOccurred = true;
	}

	// Calculate new scroll positions (opposite direction for natural feel)
	var newScrollLeft = rightDragStartScrollLeft - deltaX;
	var newScrollTop = rightDragStartScrollTop - deltaY;

	// Apply the new scroll positions
	$('#modelo-wrapper').scrollLeft(newScrollLeft);
	$('#modelo-wrapper').scrollTop(newScrollTop);

	// Update minimap viewport
	updateMinimapViewport();

	// Check for layout changes
	checkForLayoutChanges();
}

function stopRightMouseDrag() {
	isRightMouseDragging = false;

	// If a drag occurred, prevent context menu for a short time
	if (rightDragOccurred) {
		preventContextMenu = true;
		// Clear the prevention flag after a short delay to allow normal right-clicks later
		setTimeout(function() {
			preventContextMenu = false;
		}, 100);
	}

	// Restore cursor
	$('#modelo-wrapper').css('cursor', '');
	$('#divModelo').css('cursor', '');

	// Re-enable text selection
	$('body').css({
		'-webkit-user-select': '',
		'-moz-user-select': '',
		'-ms-user-select': '',
		'user-select': ''
	});
}

// Change tracking functions
function initializeChangeTracking() {
	// Capture initial layout state
	captureInitialLayoutState();

	// Capture initial model state
	captureInitialModelState();

	// Reset change indicators
	hasLayoutChanges = false;
	hasModelChanges = false;
	updateChangeIndicators();
}

function captureInitialLayoutState() {
	var layoutState = {
		positions: {},
		zoom: currentScale,
		scrollLeft: $('#modelo-wrapper').scrollLeft(),
		scrollTop: $('#modelo-wrapper').scrollTop(),
		showing: showing,
		minimap: $('#minimap-wrapper').is(':visible'),
		escuro: $('body').hasClass('escuro')
	};

	// Capture table positions
	$('.sttable').each(function() {
		var tableId = $(this).attr('id');
		var left = parseInt($(this).css('left'));
		var top = parseInt($(this).css('top'));
		layoutState.positions[tableId] = [left, top];
	});

	initialLayoutState = JSON.stringify(layoutState);
}

function captureInitialModelState() {
	// Create a simplified representation of the model state
	var modelState = {
		entities: JSON.parse(JSON.stringify(tbDefs)),
		relationships: JSON.parse(JSON.stringify(relats))
	};

	initialModelState = JSON.stringify(modelState);
}

function getCurrentLayoutState() {
	var layoutState = {
		positions: {},
		zoom: currentScale,
		scrollLeft: $('#modelo-wrapper').scrollLeft(),
		scrollTop: $('#modelo-wrapper').scrollTop(),
		showing: showing,
		minimap: $('#minimap-wrapper').is(':visible'),
		escuro: $('body').hasClass('escuro')
	};

	// Capture current table positions
	$('.sttable').each(function() {
		var tableId = $(this).attr('id');
		var left = parseInt($(this).css('left'));
		var top = parseInt($(this).css('top'));
		layoutState.positions[tableId] = [left, top];
	});

	return JSON.stringify(layoutState);
}

function getCurrentModelState() {
	var modelState = {
		entities: JSON.parse(JSON.stringify(tbDefs)),
		relationships: JSON.parse(JSON.stringify(relats))
	};

	return JSON.stringify(modelState);
}

function checkForLayoutChanges() {
	if (!initialLayoutState) return;

	var currentState = getCurrentLayoutState();
	var hasChanges = currentState !== initialLayoutState;

	if (hasChanges !== hasLayoutChanges) {
		hasLayoutChanges = hasChanges;
		updateChangeIndicators();
	}
}

function checkForModelChanges() {
	if (!initialModelState) return;

	var currentState = getCurrentModelState();
	var hasChanges = currentState !== initialModelState;

	if (hasChanges !== hasModelChanges) {
		hasModelChanges = hasChanges;
		updateChangeIndicators();
	}
}

function updateChangeIndicators() {
	// Update layout change indicator
	if (hasLayoutChanges) {
		$('#layoutChangeIndicator').show();
	} else {
		$('#layoutChangeIndicator').hide();
	}

	// Update model change indicator
	if (hasModelChanges) {
		$('#modelChangeIndicator').show();
	} else {
		$('#modelChangeIndicator').hide();
	}
}

function markLayoutSaved() {
	// Recapture the current state as the new baseline
	captureInitialLayoutState();
	hasLayoutChanges = false;
	updateChangeIndicators();
}

function markModelSaved() {
	// Recapture the current state as the new baseline
	captureInitialModelState();
	hasModelChanges = false;
	updateChangeIndicators();
}

function toggleClaroEscuro(to){
	if (to==undefined) to=$('body').hasClass('claro') ? "escuro" : "claro";
	if ($('body').hasClass(to)) return;
	$('body').toggleClass('claro escuro', to);
	document.documentElement.style.display = 'none';
	document.documentElement.setAttribute(
		"data-color-scheme",
		to=="claro" ? "light" : "dark"
	);
	document.body.clientWidth;
	document.documentElement.style.display = '';
	setTimeout(drawFkLines,500); // parece haver um delay ao trocar de claro para escuro via classe do body

	// Check for layout changes after theme toggle
	checkForLayoutChanges();
}

function clickToSelFk(e, info=false){
	// Get mouse position relative to model container
	var containerOffset = $(modelContainer).offset();
	clickX = (e.pageX - containerOffset.left)/currentScale;
	clickY = (e.pageY - containerOffset.top)/currentScale;
	
	// Distance threshold in pixels (adjust as needed)
	var threshold = 7;
	var selectedKeys = [];
	var h='';
	for (var key in fkcoords) {
		var coord = fkcoords[key];
		
		// First, do a quick bounding box check (with some padding for the threshold)
		var minX = Math.min(coord.x1, coord.x2) - threshold;
		var maxX = Math.max(coord.x1, coord.x2) + threshold;
		var minY = Math.min(coord.y1, coord.y2) - threshold;
		var maxY = Math.max(coord.y1, coord.y2) + threshold;
		
		if (clickX >= minX && clickX <= maxX && clickY >= minY && clickY <= maxY) {
			// If point is within the bounding box, calculate precise distance
			var distance = distanceToLineSegment(
				clickX, clickY,
				coord.x1, coord.y1,
				coord.x2, coord.y2
			);
			
			// If distance is less than threshold, select the FK
			if (distance <= threshold) {
				selectedKeys.push(key);
			}
		}
	}
	if (selectedKeys.length>0){
		if (info){
			for (var i=0; i<selectedKeys.length; i++){
				var key = selectedKeys[i];
				var parts=key.split('.');
				var relId = parts[0];
				var sourceTable = relats[relId].sourceTable;
				var targetTable = relats[relId].targetTable;

				var sourceCols = [];
				var targetCols = [];
				var colsDescr = [];
				for (var j=0; j<relats[relId].columns.length; j++){
					var parentCol = relats[relId].columns[j].parent;
					var childCol = relats[relId].columns[j].child;
					sourceCols.push(relats[relId].columns[j].parent);
					targetCols.push(relats[relId].columns[j].child);
					colsDescr.push('<b>'+parentCol+'</b> ' + tbDefs[sourceTable].columns[parentCol].logical + ' => <b> '+childCol+'</b> ' + tbDefs[targetTable].columns[childCol].logical);
				}
				h+='<div class="titleTooltip">'+relId+'</div><b>'+ sourceTable + '</b> ('+tbDefs[sourceTable].logical+') =&gt; <b>' + targetTable + '</b> ('+tbDefs[targetTable].logical+')<br/>'+colsDescr.join('<br/>');
				if ( relats[relId].phrase) h+='<br/>Frase: <b>'+ relats[relId].phrase+'</b> - Inversa: <b>'+ relats[relId].inverse+'</b>';
			}
			displayTooltip(e, h);
		}else{
			if (selectedKeys.length==1){
				var key = selectedKeys[0];
				var parts=key.split('.');
				var relId = parts[0];
				// var parts = key.split('.');
				// var sourceTable = parts[0];
				// var sourceCol = parts[1];
				// var targetTable = parts[2];
				// var targetCol = parts[3];
				// console.log('FK from ' + sourceTable + ' to ' + targetTable);
				// editFk(sourceTable, sourceCol, targetTable, targetCol);
				editFk(relId);
			}else{
				var relIds=[...new Set(selectedKeys.map(key => key.split('.')[0]))];
				editFks(relIds);
			}
		}
	}
}
function displayTooltip(e, text){
	// Get mouse position relative to model container
	var containerOffset = $(modelContainer).offset();
	var tooltipX = (e.pageX - containerOffset.left)/currentScale;
	var tooltipY = (e.pageY - containerOffset.top)/currentScale;
	var tooltipX = e.clientX+16;
	var tooltipY = e.clientY;
	// console.log(text);
	
	// Set tooltip position
	$('#tooltipModelo').css({
		'top': tooltipY + 'px',
		'left': tooltipX + 'px'
	}).html(text).show();
}
function hideTooltip(){
	$('#tooltipModelo').hide();
}
// Helper function to calculate distance from point to line segment
function distanceToLineSegment(px, py, x1, y1, x2, y2) {
	// Calculate length of line segment
	var lineLength = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
	
	if (lineLength === 0) {
		// Line segment is actually a point
		return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
	}
	
	// Calculate projection of point onto line
	var t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / (lineLength * lineLength);
	
	if (t < 0) {
		// Point is beyond the 'x1,y1' end of the line segment
		return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
	}
	
	if (t > 1) {
		// Point is beyond the 'x2,y2' end of the line segment
		return Math.sqrt((px - x2) * (px - x2) + (py - y2) * (py - y2));
	}
	
	// Projection falls on the line segment
	var projectionX = x1 + t * (x2 - x1);
	var projectionY = y1 + t * (y2 - y1);
	
	return Math.sqrt((px - projectionX) * (px - projectionX) + (py - projectionY) * (py - projectionY));
}
function modResize(){
	// var dmt=$('#divModelo').offset().top;
	var wh=$(window).height();
	var fh=$('#boxfoot').outerHeight();
	var mwt=$('#mapWrapper').offset().top;
	var posf=$('#boxfoot').position();

	var mt=$('#maptools').outerHeight();
	// var mch=$('#mapWrapper').outerHeight();
	$('#mapWrapper').css('height',(wh-Math.ceil(mwt)-Math.ceil(mt))+'px');
}

// Update the applyZoomToContainer function to properly resize the canvas
function applyZoomToContainer(scale) {
	// Apply transform to the container
	$('#divModelo').css({
		'transform': 'scale(' + scale + ')',
		'transform-origin': '0 0'
	});
}

function loadTables(){
	$.ajax({
		url:'ajax_modelo.php',
		data:{m:'getTables', file:modelFile},
		dataType:'json',
		success: function(response){
			// Handle the new JSON structure
			if(response.entities) {
				tbDefs = response.entities;
				relats = response.relationships;
				if (response?.modelDefs) modelDefs = response.modelDefs;
			} else {
				tbDefs = response;
				relats = response.relationships;
				if (response?.modelDefs) modelDefs = response.modelDefs;
			}
			if (response.positions){
				tbPositions = response.positions;
			}
			for(var sourceTable in tbDefs) {
				var tableDef = tbDefs[sourceTable];
				var pos;
				var numCols=0;
				for(var sourceCol in tableDef.columns) {
					var colDef = tableDef.columns[sourceCol];
					if(colDef.fk) {
						if (!tbDefs[colDef.fk.table].columns[colDef.fk.column]?.fkrefs) 
							tbDefs[colDef.fk.table].columns[colDef.fk.column].fkrefs = [];
						tbDefs[colDef.fk.table].columns[colDef.fk.column].fkrefs.push({table:sourceTable, column:sourceCol});
					}
					numCols++;
				}
				if (usaPositions) {
					if (tbPositions && tbPositions[sourceTable]) {
						pos = tbPositions[sourceTable];
					} else if (tableDef?.position) {
						pos = tableDef.position;
					} else {
						pos = [10, 10];
					}
				}else{
					if (tableDef?.position) {
						pos = tableDef.position;
					}else if (tbPositions && tbPositions[sourceTable]) {
						pos = tbPositions[sourceTable];
					} else {
						pos = [10, 10];
					}
				}
				if (maiorLeft==null || maiorLeft<pos[0]) {maiorLeft=pos[0];}
				if (maiorTop==null || maiorTop<pos[1]) {maiorTop=pos[1];}
				if (maiorBottom==null || maiorBottom<pos[1]+numCols*20+20) {maiorBottom=pos[1]+numCols*20+20;}
			}
			if (maiorLeft==null) maiorLeft=200;
			if (maiorTop==null) maiorTop=200;
			modelSize.h=maiorBottom;
			modelSize.w=maiorLeft+100;
			$('#divModelo').css('height',modelSize.h+'px');
			$('#divModelo').css('width',modelSize.w+'px');
			modResize();
	
			// Only draw tables once
			if ($('.sttable').length === 0) {
				console.log("Drawing tables");
				drawTables();
			}

			$('#minimap-wrapper').height(modelSize.h*minimapprop);
			$('#minimap').height(modelSize.h*minimapprop);
			
			console.log("Drawing FK lines on load");
			// if (tbPositions.escuro) toggleClaroEscuro('escuro'); else toggleClaroEscuro('claro');
			drawFkLines();
			
			// toggleLogPhys('list','logical');

			// Restore viewport settings if available
			restoreViewportSettings();
			updateMinimapViewport();
		}
	});
}
function drawTables(){
	// Clear existing tables if any
	$('.sttable').remove();
	$('#minimap').empty();
	
	for(var tb in tbDefs){
		drawTable(tb, tbDefs[tb]);
	}
	$('#divModelo').css('height',modelSize.h+'px');
	$('#divModelo').css('width',modelSize.w+'px');
	minimapprop=minimapwidth/modelSize.w;
	for (var tb in mmTbs){
		var tbDiv = document.createElement('div');
		tbDiv.id = tb+'_mm';
		tbDiv.className='mmtable';

		var width=mmTbs[tb].w;
		var height=mmTbs[tb].h;
		var left=mmTbs[tb].l;
		var top=mmTbs[tb].t;
		var tbl=left*minimapprop;
		var tbt=top*minimapprop;
		var tbw=width*minimapprop;
		var tbh=height*minimapprop;
		tbDiv.style.width=Math.floor(tbw)+'px';
		tbDiv.style.height=Math.floor(tbh)+'px';
		tbDiv.style.left=Math.floor(tbl)+'px';
		tbDiv.style.top=Math.floor(tbt)+'px';
		tbDiv.title=tb;
		$('#minimap').append(tbDiv);
	}
}
function updateMinimap(){
	$('#minimap').empty();
	for (var tb in mmTbs){
		var tbDiv = document.createElement('div');
		tbDiv.id = tb+'_mm';
		if (selectedTable.includes(tb)) {
			tbDiv.className='mmtable selected';
		}else{
			tbDiv.className='mmtable';
		}

		var width=mmTbs[tb].w;
		var height=mmTbs[tb].h;
		var left=mmTbs[tb].l;
		var top=mmTbs[tb].t;
		var tbl=left*minimapprop;
		var tbt=top*minimapprop;
		var tbw=width*minimapprop;
		var tbh=height*minimapprop;
		tbDiv.style.width=Math.floor(tbw)+'px';
		tbDiv.style.height=Math.floor(tbh)+'px';
		tbDiv.style.left=Math.floor(tbl)+'px';
		tbDiv.style.top=Math.floor(tbt)+'px';
		tbDiv.title=tb;
		$('#minimap').append(tbDiv);
	}
}
function redrawTable(tb){
	var defs=tbDefs[tb];
	if (document.getElementById(tb)) {
		$('#'+tb).html('');
	}
	drawTable(tb, defs);
}
function drawTable(tb, defs){
	var tbDiv;
	if (document.getElementById(tb)) {
		tbDiv = document.getElementById(tb);
		// if (tb=='TB2_TELE') debugger;
	}else{
		tbDiv = document.createElement('div');
		tbDiv.id = tb;
		// if (tb=='TB2_TELE') debugger;
	}
	tbDiv.className = 'sttable';
	// if (tb=='TB2_TELE') debugger;
	if (usaPositions){
		if (tbPositions && tbPositions[tb]) {
			tbDiv.style.left = tbPositions[tb][0] + 'px';
			tbDiv.style.top = tbPositions[tb][1] + 'px';
		}else if (defs?.position) {
			tbDiv.style.left = defs.position[0] + 'px';
			tbDiv.style.top = defs.position[1] + 'px';
		}else {
			tbDiv.style.left = '10px';
			tbDiv.style.top = '10px';
		}
	}else{
		if (defs?.position) {
			tbDiv.style.left = defs.position[0] + 'px';
			tbDiv.style.top = defs.position[1] + 'px';
		}else if (tbPositions && tbPositions[tb]) {
			tbDiv.style.left = tbPositions[tb][0] + 'px';
			tbDiv.style.top = tbPositions[tb][1] + 'px';
			
			// Update the position in tbDefs to match
			defs.position = [tbPositions[tb][0], tbPositions[tb][1]];
		} else {
			// Use position from tbDefs if no saved position exists
			tbDiv.style.left = '10px';
			tbDiv.style.top = '10px';
			defs.position = [10, 10];
		}
	}
	var colsUnique={};
	if (defs?.constraints){
		for(var ct in defs.constraints){
			if (defs.constraints[ct].type=='unique') {
				for(var col of defs.constraints[ct].columns){
					if (colsUnique[col]) colsUnique[col]++; else colsUnique[col]=1;
				}
			}
		}
	}
	
	var nameDiv = document.createElement('div');
	nameDiv.className = 'tbName';
	nameDiv.id = tb + '_name';
	nameDiv.title = defs.logical;
	nameDiv.innerHTML = '<span class="physical">'+tb+'</span><span class="logical">'+defs.logical+'</span>';
	tbDiv.appendChild(nameDiv);
	var pkDiv=null;
	for (var col in defs.columns){
		var cDef = defs.columns[col];
		if (!cDef?.pk) continue;
		if (!pkDiv) {
			pkDiv = document.createElement('div');
			pkDiv.className = 'stpks';
			pkDiv.id = tb + '_pk';
			tbDiv.appendChild(pkDiv);
		}
		var colDiv = document.createElement('div');
		colDiv.className = 'stcolumn';
		colDiv.id = tb + '_' + col;
		plus = '';
		if (colsUnique[col]) plus = (colsUnique[col]==1?' <div class="unique">U</div>':' <div class="unique">U</div>');
		if (colsUnique[col]) plus = ' <span style="color:blue;font-size:1em;">&#9930;</span>';
		html = '<span class="physical">'+col+plus+'</span><span class="logical">'+cDef.logical+plus+'</span>';
		colDiv.innerHTML = html;
		colDiv.title = cDef.logical + '\n' + cDef.type + 
			(cDef?.size ? '(' + cDef.size + ')' : '') + 
			(cDef?.nullable ? ' | null ' : ' | NOT NULL') + 
			(cDef?.pk ? ' | pk': '') + 
			(cDef?.sequence ? ' | seq' : '');
		if (cDef?.pk) colDiv.className += ' pk';
		// if (tb=='TB1_HGGL') debugger;
		if (cDef?.fkrefs) {
			var refs='';
			for (var i=0; i<cDef.fkrefs.length; i++) {
				var rel=relats[cDef.fkrefs[i]];
				for (var k=0; k<rel.columns.length; k++){
					if (rel.columns[k].child==col) refs += '\n' + rel.sourceTable + '.' + rel.columns[k].parent;
				}
			}
			colDiv.title += '\nFKs: ' + refs;
			colDiv.className += ' fk';
		}
		if (cDef?.nullable) colDiv.className += ' nullable';
		pkDiv.appendChild(colDiv);
	}
	for (var col in defs.columns){
		var cDef = defs.columns[col];
		if (cDef?.pk) continue; // Skip PK columns already drawn above
		var colDiv = document.createElement('div');
		colDiv.className = 'stcolumn';
		colDiv.id = tb + '_' + col;
		plus = '';
		if (colsUnique[col]) plus = (colsUnique[col]==1?' <div class="unique">U</div>':' <div class="unique">U</div>');
		if (colsUnique[col]) plus = ' <span style="color:blue;font-size:1em;">&#9930;</span>';
		html = '<span class="physical">'+col+plus+'</span><span class="logical">'+cDef.logical+plus+'</span>';
		colDiv.innerHTML = html;
		// colDiv.innerHTML = '<span class="physical">'+col+'</span><span class="logical">'+cDef.logical+'</span>';
		colDiv.title = cDef.logical + '\n' + cDef.type + 
			(cDef?.size ? '(' + cDef.size + ')' : '') + 
			(cDef?.nullable ? ' | null ' : ' | NOT NULL') + 
			(cDef?.pk ? ' | pk': '') + 
			(cDef?.sequence ? ' | seq' : '');
		if (cDef?.pk) colDiv.className += ' pk';
		// if (tb=='TB1_HGGL') debugger;
		if (cDef?.fkrefs) {
			var refs='';
			for (var i=0; i<cDef.fkrefs.length; i++) {
				var rel=relats[cDef.fkrefs[i]];
				for (var k=0; k<rel.columns.length; k++){
					if (rel.columns[k].child==col) refs += '\n' + rel.sourceTable + '.' + rel.columns[k].parent;
				}
			}
			colDiv.title += '\nFKs: ' + refs;
			colDiv.className += ' fk';
		}
		if (cDef?.nullable) colDiv.className += ' nullable';
		tbDiv.appendChild(colDiv);
	}
	$(tbDiv).on('click', tableClick);
	$(tbDiv).on('dblclick', function(e) {
		e.stopPropagation();
		hideTooltip();
		editTable(tb);
		// selectTable(tb, false);
	});
	
	modelContainer.appendChild(tbDiv);
	if ($(tbDiv).position().top+$(tbDiv).height()>modelSize.h-defaultModelMargin) modelSize.h=$(tbDiv).position().top+$(tbDiv).height()+defaultModelMargin;
	if ($(tbDiv).position().left+$(tbDiv).width()>modelSize.w-defaultModelMargin) modelSize.w=$(tbDiv).position().left+$(tbDiv).width()+defaultModelMargin;
	
	var mw=$('#'+tb).width();
	var mh=$('#'+tb).height();
	mmTbs[tb]={w:mw, h:mh, l:parseInt(tbDiv.style.left), t:parseInt(tbDiv.style.top)};

	// Make the table draggable
	makeDraggable(tbDiv);
}
function tableClick(e){
	e.stopPropagation();
	if (onCreateRel) {
		if (!createdRel.from) {
			createdRel.from=this.id;
			$('#divModelo').removeClass('relParent').addClass('relChild');
		}else if (!createdRel.to) {
			createdRel.to=this.id;
			$('#divModelo').removeClass('relChild');
			createRelationship();
		}
		return;
	}
	if ($(this).attr('justdragged')) {
		$(this).removeAttr('justdragged');
		return;
	}
	console.log('click '+this.id);
	selectTable(this.id, e.ctrlKey);
}
function selectTable(tableId, addToSelection) {
	$('#' + tableId).removeAttr('justdragged');
	console.log('selectTable '+tableId+' '+addToSelection);
	// If not adding to selection, clear current selection
	if (!addToSelection) {
		$('.selected').removeClass('selected');
		selectedTable = [];
	}
	
	// Add table to selection if not already selected
	if (!selectedTable.includes(tableId)) {
		selectedTable.push(tableId);
		$('#' + tableId).addClass('selected');
		$('#' + tableId+'_mm').addClass('selected');
	}else{
		selectedTable.splice(selectedTable.indexOf(tableId), 1);
		$('#' + tableId).removeClass('selected');
		$('#' + tableId+'_mm').removeClass('selected');
	}
	
	// Update info display with selection details
	if (selectedTable.length === 1) {
		$('#infotable').html('Table: ' + tableId);
	} else {
		$('#infotable').html('Selected tables: ' + selectedTable.length);
	}
	$('.tablebox.logical').each(function(k, el){
		var id=el.id.replace('_logical', '');
		if (selectedTable.includes(id)) $(el).addClass('selected'); else $(el).removeClass('selected');
	});
	$('.tablebox.physical').each(function(k, el){
		var id=el.id.replace('_physical', '');
		if (selectedTable.includes(id)) $(el).addClass('selected'); else $(el).removeClass('selected');
	});
	if ($('.tablebox.selected')[0]) $('.tablebox.selected')[0].scrollIntoViewIfNeeded();
}

function centralizar(){ //rever
	// Center the view on the tables
	var container = $('#divModelo');
	
	// Find the bounds of all tables
	var minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
	
	$('.sttable').each(function() {
		var left = parseInt($(this).css('left'));
		var top = parseInt($(this).css('top'));
		var width = $(this).outerWidth();
		var height = $(this).outerHeight();
		
		minX = Math.min(minX, left);
		minY = Math.min(minY, top);
		maxX = Math.max(maxX, left + width);
		maxY = Math.max(maxY, top + height);
	});
	
	// Calculate center point
	var centerX = (minX + maxX) / 2;
	var centerY = (minY + maxY) / 2;
	
	// Calculate container center
	var containerWidth = container.width();
	var containerHeight = container.height();
	
	// Set scroll position to center the content
	container.scrollLeft(centerX - containerWidth / 2);
	container.scrollTop(centerY - containerHeight / 2);
}

// Function to create the canvas for FK lines
function createFkCanvas() { // rever - tamanho do canvas
	// Remove any existing canvas
	$('#fkCanvas').remove();
	
	// Create a new canvas element
	var canvas = document.createElement('canvas');
	canvas.id = 'fkCanvas';
	canvas.style.position = 'absolute';
	canvas.style.top = '0';
	canvas.style.left = '0';
	canvas.style.pointerEvents = 'none';
	canvas.style.zIndex = '1';
	
	// Set canvas size to match container's unscaled dimensions
	canvas.width = modelContainer.scrollWidth;
	canvas.height = modelContainer.scrollHeight;
	
	// Apply current scale to canvas
	canvas.style.transform = 'scale(' + currentScale + ')';
	canvas.style.transformOrigin = '0 0';
	
	// Insert canvas before the first table (so tables appear on top)
	modelContainer.insertBefore(canvas, modelContainer.firstChild);

	// Create another canvas element for temporary use
	var tempCanvas = document.createElement('canvas');
	tempCanvas.id = 'fkCanvasTemp';
	tempCanvas.style.position = 'absolute';
	tempCanvas.style.top = '0';
	tempCanvas.style.left = '0';
	tempCanvas.style.pointerEvents = 'none';
	tempCanvas.style.zIndex = '1';
	tempCanvas.width = modelContainer.scrollWidth;
	tempCanvas.height = modelContainer.scrollHeight;
	tempCanvas.style.transform = 'scale(' + currentScale + ')';
	tempCanvas.style.transformOrigin = '0 0';
	
	// Insert temp canvas before the first table (so tables appear on top)
	modelContainer.insertBefore(tempCanvas, modelContainer.firstChild);

}

// Function to resize the temporary FK canvas
function resizeTempFkCanvas() {
	var tempCanvas = document.getElementById('fkCanvasTemp');
	if (tempCanvas) {
		// Set temp canvas size to match the container's actual dimensions
		tempCanvas.width = modelContainer.scrollWidth;
		tempCanvas.height = modelContainer.scrollHeight;
	}
}

// Function to resize the FK canvas
function resizeFkCanvas() {
	var canvas = document.getElementById('fkCanvas');
	if (canvas) {
		// Set canvas size to match the container's actual dimensions
		canvas.width = modelContainer.scrollWidth;
		canvas.height = modelContainer.scrollHeight;
	}
	var tempCanvas = document.getElementById('fkCanvasTemp');
	if (tempCanvas) {
		// Set temp canvas size to match the container's actual dimensions
		tempCanvas.width = modelContainer.scrollWidth;
		tempCanvas.height = modelContainer.scrollHeight;
	}
}

// Function to draw FK relationship lines on a temporary canvas when dragging table
function drawFkLinesTemp(tbs=[]) {
	// Get the temp canvas and context
	var tempCanvas = document.getElementById('fkCanvasTemp');
	if (!tempCanvas) {
		createFkCanvas();
		tempCanvas = document.getElementById('fkCanvasTemp');
	} else {
		// Ensure temp canvas size is correct
		if (tempCanvas.width !== modelContainer.scrollWidth || 
			tempCanvas.height !== modelContainer.scrollHeight) {
			resizeTempFkCanvas();
		}
	}
	var ctx = tempCanvas.getContext('2d');
	
	// Clear the temp canvas
	ctx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
	
	// Draw FK lines to and from the dragged table
	for(var fkname in relats){
		var rel=relats[fkname];
		if (tbs.includes(rel.sourceTable) || tbs.includes(rel.targetTable)) {
			for (var k=0; k<rel.columns.length; k++){
				drawFkLine(ctx, fkname, rel.sourceTable, rel.columns[k].parent, rel.targetTable, rel.columns[k].child, true);
			}
		}
	}
}

// Function to clear temporary FK lines
function clearTempFkLines() {
	var tempCanvas = document.getElementById('fkCanvasTemp');
	if (tempCanvas) {
		var ctx = tempCanvas.getContext('2d');
		ctx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
	}
}
var fkcoords={};
// Function to draw FK relationship lines
function drawFkLines(excludes=[]) {
	// Get the canvas and context
	var canvas = document.getElementById('fkCanvas');
	if (!canvas) {
		createFkCanvas();
		canvas = document.getElementById('fkCanvas');
	} else {
		// Ensure canvas size is correct
		if (canvas.width !== modelContainer.scrollWidth || 
			canvas.height !== modelContainer.scrollHeight) {
			resizeFkCanvas();
		}
	}
	
	var ctx = canvas.getContext('2d');
	
	// Clear the canvas
	ctx.clearRect(0, 0, canvas.width, canvas.height);
	
	// Remove any existing phrase indicators
	$('.phrase-indicator, .inverse-phrase-indicator').remove();
	
	// Draw all FK lines
	fkcoords={};
	var fkcoord;
	for(var fkname in relats){
		var rel=relats[fkname];
		if (excludes.includes(rel.sourceTable) || excludes.includes(rel.targetTable)) continue;
		for (var k=0; k<rel.columns.length; k++){
			fkcoord=drawFkLine(ctx, fkname, rel.sourceTable, rel.columns[k].parent, rel.targetTable, rel.columns[k].child);
			// if (excludes.length==0) fkcoords[rel.sourceTable+'.'+rel.columns[k].parent+'.'+rel.targetTable+'.'+rel.columns[k].child]=fkcoord;
			if (excludes.length==0 && !fkcoords[fkname+'.'+rel.columns[k].child]) fkcoords[fkname+'.'+rel.columns[k].child]=fkcoord;
		}
	}
}
var fkLinePositions={};
// Function to draw a single FK relationship line
function drawFkLine(ctx, fkname, parentTable, parentCol, childTable, childCol, temp=false) {
	// Get source and target elements
	var childElem = document.getElementById(childTable + '_' + childCol);
	var parentElem = document.getElementById(parentTable + '_' + parentCol);
	
	if (!childElem || !parentElem) {
		console.log("Cannot find elements for", parentTable, parentCol, childTable, childCol);
		return;
	}
	
	// Get column definition to check for phrase attributes and nullable property
	var colDef = tbDefs[childTable].columns[childCol];
	var rel=relats[fkname];
	var hasPhrase = rel.phrase !== undefined;
	var hasInverse = rel.inverse !== undefined;
	var isNullable = colDef.nullable === true;
	
	// Get positions
	var childRect = childElem.getBoundingClientRect();
	var parentRect = parentElem.getBoundingClientRect();
	var containerRect = modelContainer.getBoundingClientRect();
	
	// Get parent table elements
	var childTableElem = document.getElementById(childTable);
	var parentTableElem = document.getElementById(parentTable);
	
	if (!childTableElem || !parentTableElem) return;
	
	var childTableRect = childTableElem.getBoundingClientRect();
	var parentTableRect = parentTableElem.getBoundingClientRect();

	var mcScrollLeft = modelContainer.scrollLeft*currentScale;
	var mcScrollTop = modelContainer.scrollTop*currentScale;
	
	// Calculate center points - no need to account for scaling since canvas is already scaled
	var childCenterX = (childRect.left + childRect.width/2 - containerRect.left) + mcScrollLeft;
	var childCenterY = (childRect.top + childRect.height/2 - containerRect.top) + mcScrollTop;
	var parentCenterX = (parentRect.left + parentRect.width/2 - containerRect.left) + mcScrollLeft;
	var parentCenterY = (parentRect.top + parentRect.height/2 - containerRect.top) + mcScrollTop;
	
	// Calculate table edges - no need to account for scaling
	var childTableLeft = (childTableRect.left - containerRect.left) + mcScrollLeft;
	var childTableTop = (childTableRect.top - containerRect.top) + mcScrollTop;
	var childTableRight = childTableLeft + childTableRect.width;
	var childTableBottom = childTableTop + childTableRect.height;
	
	var parentTableLeft = (parentTableRect.left - containerRect.left) + mcScrollLeft;
	var parentTableTop = (parentTableRect.top - containerRect.top) + mcScrollTop;
	var parentTableRight = parentTableLeft + parentTableRect.width;
	var parentTableBottom = parentTableTop + parentTableRect.height;
	
	// Calculate the angle between centers
	var angle = Math.atan2(parentCenterY - childCenterY, parentCenterX - childCenterX);
	
	// Find intersection points with table edges
	var x1, y1, x2, y2;
	
	// Child intersection (FK side)
	if (Math.abs(Math.cos(angle)) > Math.abs(Math.sin(angle))) {
		// Mostly horizontal movement
		if (Math.cos(angle) > 0) {
			// Moving right
			x1 = childTableRight;
			y1 = childCenterY;
		} else {
			// Moving left
			x1 = childTableLeft;
			y1 = childCenterY;
		}
	} else {
		// Mostly vertical movement
		if (Math.sin(angle) > 0) {
			// Moving down
			x1 = childCenterX;
			y1 = childTableBottom;
		} else {
			// Moving up
			x1 = childCenterX;
			y1 = childTableTop;
		}
	}
	
	// Parent intersection (PK side)
	if (Math.abs(Math.cos(angle)) > Math.abs(Math.sin(angle))) {
		// Mostly horizontal movement
		if (Math.cos(angle) > 0) {
			// Coming from left
			x2 = parentTableLeft;
			y2 = parentCenterY;
		} else {
			// Coming from right
			x2 = parentTableRight;
			y2 = parentCenterY;
		}
	} else {
		// Mostly vertical movement
		if (Math.sin(angle) > 0) {
			// Coming from top
			x2 = parentCenterX;
			y2 = parentTableTop;
		} else {
			// Coming from bottom
			x2 = parentCenterX;
			y2 = parentTableBottom;
		}
	}
	// Adjust the coordinates if necessary to use the center Y of the table when the tables are sufficiently side by side
	if (childTableRight+childTableRect.width/2<parentTableLeft) {
		x1=childTableRight;
		x2=parentTableLeft;
		y1=childCenterY;
		y2=parentCenterY;
	}else if (childTableLeft-childTableRect.width/2>parentTableRight){
		x1=childTableLeft;
		x2=parentTableRight;
		y1=childCenterY;
		y2=parentCenterY;
	}
	// Scale the coordinates
	x2/=currentScale;
	y2/=currentScale;
	x1/=currentScale;
	y1/=currentScale;

	// Recalculate angle based on intersection points
	angle = Math.atan2(y2 - y1, x2 - x1);
	
	// Set line properties
	ctx.beginPath();
	var dashPattern = isNullable ? [4, 4] : [];
	if ($('body').hasClass('escuro')){
		dashPattern = isNullable ? [6, 6] : [];
		if(temp) {
			if (isNullable){
				ctx.strokeStyle = '#333333'; // Gray color for temporary lines
			}else{
				ctx.strokeStyle = '#666666'; // Gray color for temporary lines
			}
		} else {
			ctx.strokeStyle = isNullable ? '#dddddd' : '#aaaaaa';
		}
	}else{
		if(temp) {
			if (isNullable){
				ctx.strokeStyle = '#888888'; // Gray color for temporary lines
			}else{
				ctx.strokeStyle = '#aaaaaa'; // Gray color for temporary lines
			}
		} else {
			ctx.strokeStyle = '#000000';
		}
	}
	ctx.setLineDash(dashPattern);
	ctx.moveTo(x1, y1);
	ctx.lineTo(x2, y2);
	ctx.lineWidth = 1; // Use fixed width
	ctx.stroke();
	
	// Reset dash pattern for arrowhead
	ctx.setLineDash([]);
	
	fkCoord={x1,y1,x2,y2};
	fkLinePositions[parentTable+'_'+parentCol+'_'+childTable+'_'+childCol]=fkCoord;
	
	// Draw arrowhead at the source end (x1, y1)
	var arrowSize = 8; // Fixed size
	
	// Calculate the angle from target to source (reversed direction)
	var arrowAngle = Math.atan2(y1 - y2, x1 - x2);
	
	ctx.beginPath();
	ctx.moveTo(x1, y1);
	ctx.lineTo(
		x1 - arrowSize * Math.cos(arrowAngle - Math.PI/6),
		y1 - arrowSize * Math.sin(arrowAngle - Math.PI/6)
	);
	ctx.lineTo(
		x1 - arrowSize * Math.cos(arrowAngle + Math.PI/6),
		y1 - arrowSize * Math.sin(arrowAngle + Math.PI/6)
	);
	ctx.closePath();
	ctx.fillStyle = ctx.strokeStyle;
	ctx.fill();
	if (temp) return;
	// Add phrase indicators if present
	if (hasPhrase || hasInverse) {
		// Calculate midpoints for phrase indicators
		var startX = x1 + (x2 - x1) * 0.2; // 20% along the line
		var startY = y1 + (y2 - y1) * 0.2;
		var endX = x1 + (x2 - x1) * 0.8; // 80% along the line
		var endY = y1 + (y2 - y1) * 0.8;
		
		// Fixed sizes for indicators
		var indicatorPadding = 2;
		var indicatorFontSize = 10;
		
		// Add phrase indicator at start if present
		if (hasPhrase) {
			var phraseDiv = document.createElement('div');
			phraseDiv.className = 'phrase-indicator';
			phraseDiv.innerHTML = 'F';
			phraseDiv.title = rel.phrase;
			phraseDiv.style.position = 'absolute';
			phraseDiv.style.left = startX + 'px';
			phraseDiv.style.top = startY + 'px';
			phraseDiv.style.width = '20px';
			phraseDiv.style.height = '20px';
			phraseDiv.style.textAlign = 'center';
			phraseDiv.style.backgroundColor = '#fff';
			phraseDiv.style.border = '1px solid #007bff';
			phraseDiv.style.borderRadius = '50%';
			phraseDiv.style.padding = indicatorPadding + 'px ' + (indicatorPadding * 2) + 'px';
			phraseDiv.style.fontSize = indicatorFontSize + 'px';
			phraseDiv.style.color = '#007bff';
			phraseDiv.style.fontWeight = 'bold';
			phraseDiv.style.zIndex = '2';
			phraseDiv.style.cursor = 'help';
			phraseDiv.style.transform = 'translate(-50%, -50%)'; // Center on point
			phraseDiv.style.transformOrigin = 'center center';
			modelContainer.appendChild(phraseDiv);
		}
		
		// Add inverse phrase indicator at end if present
		if (hasInverse) {
			var inverseDiv = document.createElement('div');
			inverseDiv.className = 'inverse-phrase-indicator';
			inverseDiv.innerHTML = 'FI';
			inverseDiv.title = rel.inverse;
			inverseDiv.style.position = 'absolute';
			inverseDiv.style.left = endX + 'px';
			inverseDiv.style.top = endY + 'px';
			inverseDiv.style.width = '20px';
			inverseDiv.style.height = '20px';
			inverseDiv.style.textAlign = 'center';
			inverseDiv.style.backgroundColor = '#007bff';
			inverseDiv.style.border = '1px solid #007bff';
			inverseDiv.style.borderRadius = '50%';
			inverseDiv.style.padding = indicatorPadding + 'px ' + (indicatorPadding * 2) + 'px';
			inverseDiv.style.fontSize = indicatorFontSize + 'px';
			inverseDiv.style.color = '#ffffff';
			inverseDiv.style.fontWeight = 'bold';
			inverseDiv.style.zIndex = '2';
			inverseDiv.style.cursor = 'help';
			inverseDiv.style.transform = 'translate(-50%, -50%)'; // Center on point
			inverseDiv.style.transformOrigin = 'center center';
			modelContainer.appendChild(inverseDiv);
		}
	}
	return fkCoord;
}

// Extend makeDraggable to update FK lines
function makeDraggable(element) { // rever - redimensionamento da divModelo e canvas
	var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
	
	// Add cursor style to indicate draggable
	element.style.cursor = 'move';

	element.onmousedown = dragMouseDown;

	function dragMouseDown(e) {
		e = e || window.event;
		$(element).off('click', tableClick);
		$(element).removeAttr('justdragged');
		element.moveu=false;
		e.preventDefault();
		e.stopPropagation();
		// Get the mouse cursor position at startup
		pos3 = e.clientX;
		pos4 = e.clientY;
		pos1=0;
		pos2=0;
		// console.log('dragMouseDown '+pos3+' '+pos4);
		document.onmouseup = closeDragElement;
		document.onmousemove = elementDrag;
	}

	function elementDrag(e) {
		e = e || window.event;
		e.preventDefault();
		if (!selectedTable.includes(element.id)) {
			selectTable(element.id, e.ctrlKey);
		}
		var dX=0, dY=0;
		// console.log('clientY: '+e.clientY + ' wrapper top: '+$('#modelo-wrapper').offset().top + ' wrapperScrollTop: '+$('#modelo-wrapper').scrollTop());
		if (e.clientY - $('#modelo-wrapper').offset().top < 15 && $('#modelo-wrapper').scrollTop() > 10) {
			$('#modelo-wrapper').scrollTop($('#modelo-wrapper').scrollTop() - 10);
			dY-=10;
		}
		if (e.clientY - $('#modelo-wrapper').offset().top > $('#modelo-wrapper').height()-15) {
			$('#modelo-wrapper').scrollTop($('#modelo-wrapper').scrollTop() + 10);
			dY+=10;
		}
		if (e.clientX - $('#modelo-wrapper').offset().left < 15 && $('#modelo-wrapper').scrollLeft() > 15) {
			$('#modelo-wrapper').scrollLeft($('#modelo-wrapper').scrollLeft() - 10);
			dX-=10;
		}
		// console.log('pos: '+(e.clientX-$('#divModelo').offset().left)+ ' borda: '+($('#mapWrapper').width()-$('#divModelo').offset().left));
		if (e.clientX - $('#modelo-wrapper').offset().left > $('#modelo-wrapper').width() - 15) {
			$('#modelo-wrapper').scrollLeft($('#modelo-wrapper').scrollLeft() + 10);
			dX+=10;
		}

		// Calculate the new cursor position
		pos1 = pos3 - e.clientX - dX;
		pos2 = pos4 - e.clientY - dY;
		pos3 = e.clientX;
		pos4 = e.clientY;
		if (pos1!=0 || pos2!=0) {
			element.moveu=true;
		}
		if (!element.moveu) return;
		if (draggingState=='N') {
			draggingState='D';
			drawFkLines(selectedTable);
			// console.log(selectedTable.join(','));
		}
		// console.log('pos1: '+pos1+' pos2: '+pos2+' pos3: '+pos3+' pos4: '+pos4+' dX: '+dX+' dY: '+dY);
		// console.log('clientY: '+e.clientY+' wrapperScrollTop: '+$('#divModelo').offset().top);
		var posTop, posLeft;
		for (var k=0; k<selectedTable.length; k++){
			var selElement = document.getElementById(selectedTable[k]);
			var newTop = (Math.round(selElement.offsetTop - pos2/currentScale));
			var newLeft = (Math.round(selElement.offsetLeft - pos1/currentScale));
			if (selectedTable[k] == element.id) {
				posTop = newTop;
				posLeft = newLeft;
			}
			selElement.style.top = newTop + "px";
			selElement.style.left = newLeft + "px";
			var mw=$('#'+selectedTable[k]).width();
			var mh=$('#'+selectedTable[k]).height();
			var tbDiv = document.getElementById(selectedTable[k]);
			mmTbs[selectedTable[k]]={w:mw, h:mh, l:parseInt(tbDiv.style.left), t:parseInt(tbDiv.style.top)};
		}
		
		// Update the info div with position
		$('#infotable').html('Table: ' + element.id + ' - Position: [' + posLeft + ', ' + posTop + ']');
		// Update FK lines
		drawFkLinesTemp(selectedTable);
		// var mw=$('#'+element.id).width();
		// var mh=$('#'+element.id).height();
		// var tbDiv = document.getElementById(element.id);
		// mmTbs[element.id]={w:mw, h:mh, l:parseInt(tbDiv.style.left), t:parseInt(tbDiv.style.top)};
		updateMinimap();
	}

	function closeDragElement(e) {
		e = e || window.event;
		e.preventDefault();
		draggingState='N';
		// console.log('closeDragElement');
		if (element.moveu) {
			$(element).attr('justdragged', true);
			// console.log('justdragged added - moveu');
			// Update the table definition with new position
			if (tbDefs[element.id]) {
				var scaledLeft = Math.round(parseInt(element.style.left) / currentScale);
				var scaledTop = Math.round(parseInt(element.style.top) / currentScale);
				tbDefs[element.id].position = [scaledLeft, scaledTop];
			}
			var mw=$('#'+element.id).width();
			var mh=$('#'+element.id).height();
			var tbDiv = document.getElementById(element.id);
			mmTbs[element.id]={w:mw, h:mh, l:parseInt(tbDiv.style.left), t:parseInt(tbDiv.style.top)};

			if (selectedTable.length>1) {
				for (var k=0; k<selectedTable.length; k++){
					if (selectedTable[k] == element.id) continue;
					var selElement = document.getElementById(selectedTable[k]);
					var newTop = (Math.round(selElement.offsetTop - pos2/currentScale));
					var newLeft = (Math.round(selElement.offsetLeft - pos1/currentScale));
					selElement.style.top = newTop + "px";
					selElement.style.left = newLeft + "px";
					if (tbDefs[selElement.id]) {
						var scaledLeft = Math.round(parseInt(selElement.style.left) / currentScale);
						var scaledTop = Math.round(parseInt(selElement.style.top) / currentScale);
						tbDefs[selElement.id].position = [scaledLeft, scaledTop];
					}
					var mw=$('#'+selElement.id).width();
					var mh=$('#'+selElement.id).height();
					var tbDiv = document.getElementById(selElement.id);
					mmTbs[selElement.id]={w:mw, h:mh, l:parseInt(tbDiv.style.left), t:parseInt(tbDiv.style.top)};
				}
			}

			drawFkLines();
			clearTempFkLines();
			updateMinimap();

			// Check for layout changes after table movement
			checkForLayoutChanges();
		}
		// Stop moving when mouse button is released
		document.onmouseup = null;
		document.onmousemove = null;
		$(element).on('click', tableClick);
	}
}

// Current scale of the model
var currentScale = 1;
var minScale = 0.1;
var maxScale = 2;
var scaleStep = 0.05;
var originalPositions = {}; // Store original positions of tables

// New function to handle scroll events with different modifiers
function handleScrollEvent(e) { // rever - fazer o scroll no modelo-wrapper; ajustar tamanho divModelo no zoom out (reduzir no zoom in se maior do que o necessario ??)
	e.preventDefault();
	
	var delta = e.originalEvent.deltaY;
	
	// Ctrl + wheel = zoom
	if (e.ctrlKey) {
		handleZoom(e);
	} 
	// Shift + wheel = horizontal scroll
	else if (e.shiftKey) {
		$('#modelo-wrapper').scrollLeft($('#modelo-wrapper').scrollLeft() + delta);
	} 
	// No modifier = vertical scroll
	else {
		$('#modelo-wrapper').scrollTop($('#modelo-wrapper').scrollTop() + delta);
	}
		
	// Redraw FK lines to account for new scale
	drawFkLines();

	updateMinimapViewport();
	
	return false;
}

function updateMinimapViewport() {
	// Get current scroll position and container size
	var scrollLeft = $('#modelo-wrapper').scrollLeft()/currentScale;
	var scrollTop = $('#modelo-wrapper').scrollTop()/currentScale;
	var containerWidth = $('#modelo-wrapper').width()/currentScale;
	var containerHeight = $('#modelo-wrapper').height()/currentScale;
	
	// Calculate viewport position and size in minimap coordinates
	var viewportLeft = Math.round(scrollLeft * minimapprop);
	var viewportTop = Math.round(scrollTop * minimapprop);
	var viewportWidth = Math.round(containerWidth * minimapprop);
	var viewportHeight = Math.round(containerHeight * minimapprop);

	if ($('#minimap-viewport').length==0) {
		var viewport = document.createElement('div');
		viewport.id = 'minimap-viewport';
		viewport.className = 'minimap-viewport';
		$('#minimap-wrapper').append(viewport);
		
		// Make the viewport draggable
		makeMinimapViewportDraggable(viewport);
	}
	
	$('#minimap-viewport').css({
		'left': viewportLeft + 'px',
		'top': viewportTop + 'px',
		'width': viewportWidth + 'px',
		'height': viewportHeight + 'px'
	});
}

// Function to make minimap viewport draggable
function makeMinimapViewportDraggable(element) {
	var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
	
	// Add cursor style to indicate draggable
	element.style.cursor = 'move';
	
	element.onmousedown = dragMouseDown;

	function dragMouseDown(e) {
		e = e || window.event;
		e.preventDefault();
		e.stopPropagation();
		
		// Get the mouse cursor position at startup
		pos3 = e.clientX;
		pos4 = e.clientY;
		
		document.onmouseup = closeDragElement;
		document.onmousemove = elementDrag;
	}

	function elementDrag(e) {
		e = e || window.event;
		e.preventDefault();
		e.stopPropagation();
		
		// Calculate the new cursor position
		pos1 = pos3 - e.clientX;
		pos2 = pos4 - e.clientY;
		pos3 = e.clientX;
		pos4 = e.clientY;
		
		// Set the element's new position
		var newTop = Math.max(0, element.offsetTop - pos2);
		var newLeft = Math.max(0, element.offsetLeft - pos1);
		
		// Constrain to minimap bounds
		var maxLeft = $('#minimap').width() - $(element).width();
		var maxTop = $('#minimap').height() - $(element).height();
		newLeft = Math.min(newLeft, maxLeft);
		newTop = Math.min(newTop, maxTop);
		
		element.style.top = newTop + "px";
		element.style.left = newLeft + "px";
		
		// Update main view scroll position in real-time
		updateMainViewFromMinimap(newLeft, newTop);
	}

	function closeDragElement() {
		document.onmouseup = null;
		document.onmousemove = null;
	}
}

// Function to update main view scroll position based on minimap viewport position
function updateMainViewFromMinimap(left, top) {
	// Convert minimap coordinates to main view coordinates
	var scrollLeft = Math.round(left / minimapprop * currentScale);
	var scrollTop = Math.round(top / minimapprop * currentScale);
	
	// Apply scroll to main view
	$('#modelo-wrapper').scrollLeft(scrollLeft);
	$('#modelo-wrapper').scrollTop(scrollTop);
}

// Keep the existing handleZoom function but modify it to work with container scaling
function handleZoom(e) {
	// Get mouse position relative to the model wrapper
	var wrapperOffset = $('#modelo-wrapper').offset();
	var mouseX = e.pageX - wrapperOffset.left;
	var mouseY = e.pageY - wrapperOffset.top;
	
	// Get current scroll position
	var scrollLeft = $('#modelo-wrapper').scrollLeft();
	var scrollTop = $('#modelo-wrapper').scrollTop();
	
	// Calculate mouse position relative to content (accounting for scroll)
	var contentX = (mouseX + scrollLeft) / currentScale;
	var contentY = (mouseY + scrollTop) / currentScale;
	
	// Determine zoom direction
	var delta = e.originalEvent.deltaY < 0 ? 1 : -1;
	var newScale = currentScale + (delta * scaleStep);
	
	// Limit scale to min/max values
	newScale = Math.max(minScale, Math.min(maxScale, newScale));
	
	// Only proceed if scale changed
	if (newScale !== currentScale) {
		// Apply the new scale to the container
		applyZoomToContainer(newScale);
		
		// Update current scale
		var oldScale = currentScale;
		currentScale = newScale;
		
		// Calculate new scroll position to keep mouse over same content
		var newScrollLeft = (contentX * newScale) - mouseX;
		var newScrollTop = (contentY * newScale) - mouseY;
		
		// Apply new scroll position
		$('#modelo-wrapper').scrollLeft(newScrollLeft);
		$('#modelo-wrapper').scrollTop(newScrollTop);
		
		// Update info display with current zoom level
		$('#infozoom').html('Zoom: ' + Math.round(currentScale * 100) + '%');
		
		// Redraw FK lines and update minimap
		drawFkLines();
		updateMinimapViewport();

		// Check for layout changes after zoom
		checkForLayoutChanges();
	}
}

// Store original positions of all tables
function storeOriginalPositions() { // rever - armazenar mais informações?
	$('.sttable').each(function() {
		var id = $(this).attr('id');
		originalPositions[id] = {
			left: parseInt($(this).css('left')),
			top: parseInt($(this).css('top'))
		};
		
		// Also store in tbDefs if not already there
		if (!tbDefs[id].originalPosition) {
			tbDefs[id].originalPosition = [...tbDefs[id].position];
		}
	});
}


// Reset zoom to default scale
function resetZoom() {
	// Apply scale of 1 to container
	applyZoomToContainer(1);
	
	// Update current scale
	currentScale = 1;
	
	// Update info display
	$('#infozoom').html('Zoom: 100%');
	
	// Redraw FK lines
	drawFkLines();

	updateMinimapViewport();
}

function saveTablePositions() { // rever - mais informações do estado da interface e do modelo
	var positions = {};
	
	// Collect positions of all tables
	$('.sttable').each(function() {
		var tableId = $(this).attr('id');
		var left = Math.round(parseInt($(this).css('left')));
		var top = Math.round(parseInt($(this).css('top')));
		
		positions[tableId] = [left, top];
		
		// Also update the tbDefs object
		if (tbDefs[tableId]) {
			tbDefs[tableId].position = [left, top];
		}
	});
	
	// Add viewport information
	positions.viewport = {
		zoom: currentScale,
		scrollTop: $('#modelo-wrapper').scrollTop(),
		scrollLeft: $('#modelo-wrapper').scrollLeft()
	};
	positions.showing=showing;
	positions.minimap=$('#minimap-wrapper').is(':visible');
	positions.escuro=$('body').hasClass('escuro');
	
	// Convert to JSON
	var positionsJson = JSON.stringify(positions);
	
	// Send to server
	$.ajax({
		url: 'ajax_modelo.php',
		type: 'POST',
		data: {
			m: 'savePositions',
			positions: positionsJson,
			file: modelFile
		},
		success: function(response) {
			// eval(response); // Execute the response (likely an alert)
			console.log(response);

			// Mark layout as saved
			markLayoutSaved();
		},
		error: function() {
			alert('Error saving positions');
		}
	});
}


// Update centralizar function to account for scaling
function centralizar() { // rever - centralizar via scroll do modelo-wrapper
	// Center the view on the tables
	var container = $('#modelo-wrapper');
	
	// Find the bounds of all tables
	var minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
	
	$('.sttable').each(function() {
		var left = parseInt($(this).css('left'));
		var top = parseInt($(this).css('top'));
		var width = $(this).outerWidth();
		var height = $(this).outerHeight();
		
		minX = Math.min(minX, left);
		minY = Math.min(minY, top);
		maxX = Math.max(maxX, left + width);
		maxY = Math.max(maxY, top + height);
	});
	
	// Calculate center point
	var centerX = (minX + maxX) / 2 * currentScale;
	var centerY = (minY + maxY) / 2 * currentScale;
	
	// Calculate container center
	var containerWidth = container.width();
	var containerHeight = container.height();
	
	// Set scroll position to center the content
	container.scrollLeft(centerX - containerWidth / 2);
	container.scrollTop(centerY - containerHeight / 2);

	updateMinimapViewport();
}


// Function to restore viewport settings
function restoreViewportSettings() { // rever - restaurar via scroll do modelo-wrapper
	if (typeof tbPositions !== 'undefined' && tbPositions.viewport) {
		// Restore zoom level if available
		if (tbPositions.viewport.zoom && tbPositions.viewport.zoom !== currentScale) {
			// Apply the saved zoom level to container
			var savedScale = tbPositions.viewport.zoom;
			applyZoomToContainer(savedScale);
			currentScale = savedScale;
			
			// Update info display with current zoom level
			$('#infozoom').html('Zoom: ' + Math.round(currentScale * 100) + '%');
			
			// Redraw FK lines to account for scale
			drawFkLines();
		}
		
		// Restore scroll position if available
		if (tbPositions.viewport.scrollTop !== undefined) {
			$('#modelo-wrapper').scrollTop(tbPositions.viewport.scrollTop);
		}
		
		if (tbPositions.viewport.scrollLeft !== undefined) {
			$('#modelo-wrapper').scrollLeft(tbPositions.viewport.scrollLeft);
		}
		if (tbPositions.showing){
			toggleLogPhys('list',tbPositions.showing.list);
			toggleLogPhys('model',tbPositions.showing.model);
		}else{
			toggleLogPhys('list',showing.list);
			toggleLogPhys('model',showing.model);
		}
		if (tbPositions.minimap!==undefined) toggleMinimap(tbPositions.minimap);
		if (tbPositions.escuro) toggleClaroEscuro('escuro');
	}
}
function showStatus(msg, classe='sucesso'){
	$('#infostatus').html(msg).addClass(classe);
	$('#infostatus').show();
	setTimeout(function() {
		$('#infostatus').html('').removeClass(classe);
		$('#infostatus').hide();
	}, 2000);
}
function move(direction) { // rever - ajustar o tamanho do divModelo, se necessário
	var step = 10; // Adjust this value to change the step size
	var container = $('#divModelo');
	var menorLeft = null;
	var menorTop = null;
	for(var tb in tbDefs){
		var defs=tbDefs[tb];
		if(menorLeft==null || menorLeft>defs.position[0]){menorLeft=defs.position[0];}
		if(menorTop==null || menorTop>defs.position[1]){menorTop=defs.position[1];}
	}
	
	switch (direction) {
		case 'up':
			for(var tb in tbDefs){
				var defs=tbDefs[tb];
				tbDefs[tb].position[1]=defs.position[1]-step;
				tbPositions[tb][1]=defs.position[1]-step;
			}
			break;
		case 'down':
			for(var tb in tbDefs){
				var defs=tbDefs[tb];
				tbDefs[tb].position[1]=defs.position[1]+step;
				tbPositions[tb][1]=defs.position[1]+step;
			}
			break;
		case 'left':
			for(var tb in tbDefs){
				var defs=tbDefs[tb];
				tbDefs[tb].position[0]=defs.position[0]-step;
				tbPositions[tb][0]=defs.position[0]-step;
			}
			break;
		case 'right':
			for(var tb in tbDefs){
				var defs=tbDefs[tb];
				tbDefs[tb].position[0]=defs.position[0]+step;
				tbPositions[tb][0]=defs.position[0]+step;
			}
			break;
	}
	drawTables();
	drawFkLines();
}
function toggleLogPhys(qual,to){
	if (qual=='list') parent='#modelSideBar'; else parent='#divModelo';
	if (typeof to=='undefined') to=(showing[qual]=='logical'?'physical':'logical');
	if (to=='physical'){
		showing[qual]='physical';
		$((parent?parent+' ':'')+'.logical').hide();
		$((parent?parent+' ':'')+'.physical').show();
	}else{
		showing[qual]='logical';
		$((parent?parent+' ':'')+'.logical').show();
		$((parent?parent+' ':'')+'.physical').hide();
	}
	drawFkLines();
}
function selLocateTable(tableId){
	selectTable(tableId, false);
	locateTable(tableId);
}
function locateTable(tableId){
	var tbDiv = $('#' + tableId);
	var container = $('#divModelo');
	
	// Calculate center point of the table
	var centerX = tbPositions[tableId][0] + tbDiv.outerWidth() / 2;
	var centerY = tbPositions[tableId][1] + tbDiv.outerHeight() / 2;

	
	// Calculate container center
	var containerWidth = container.width();
	var containerHeight = container.height();
	
	// Set scroll position to center the table
	container.scrollLeft(centerX - containerWidth / 2);
	container.scrollTop(centerY - containerHeight / 2);
}
var editTableHtml=null;
function editTable(tableId){
	if ($('#editTableDialog').length==0){
		var md=$('<div id="editTableDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (editTableHtml==null){
		$.ajax({
			url:'ajax_modelo.php',
			type:'get',
			dataType:'html',
			data:{m:'editTable', file:modelFile, tableId:tableId},
			success:function(html){
				editTableHtml=html;
				editTable(tableId);
			}
		});
		return;
	}
	$('#editTableDialog').html(editTableHtml);
	fillEditTableDialog(tableId);
	$('#editTableDialog').dialog({
		modal:true,
		width:700,
		height:'auto',
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText:'Fechar',
		title: 'Edição da tabela '+tableId,
		dialogClass: 'default',
		buttons: [
			{
			text: "Fechar",
			click: function() {
				drawTables();
				drawFkLines();
				$( this ).dialog( "close" );
			}
			,showText: false
			}
		],
		close: function() {
			$('#editTableDialog').dialog('close').remove();
		}
	});
}
function fillEditTableDialog(tableId){
	var tabDefs=tbDefs[tableId];
	var html=$('#editTableDialog').html();
	html=html.replace(/__tableId__/g, tableId);
	html=html.replace(/__logicalName__/g, tabDefs.logical);
	html=html.replace(/__numUniques__/g, tabDefs.constraints?Object.keys(tabDefs.constraints).length:0);
	$('#editTableDialog').html(html);
	var tr=$('#templateColumnTr').clone();
	$('#templateColumnTr').remove();
	for(var col in tabDefs.columns){
		var cDef=tabDefs.columns[col];
		if (!cDef.pk) continue;
		var ico='';
		if (cDef.pk) ico+='<i class="fa fa-key"></i> ';
		if (cDef?.fkrefs && cDef.fkrefs.length>0) {
			var title='';
			var refs=[];
			for (var i=0; i<cDef.fkrefs.length; i++) {
				var relId=cDef.fkrefs[i];
				refs.push(relId);
				var frase='';
				var rel=relats[relId];
				for (var k=0; k<rel.columns.length; k++){
					if (rel.columns[k].child==col) title += (title==''?'':'\n') + rel.sourceTable + '.' + rel.columns[k].parent;
					if (rel.phrase) title+=' ('+rel.phrase+(rel.inverse?' - '+rel.inverse:'')+')';
				}
			}
			if (refs.length==1){
				ico+='<i class="fa fa-link" title="'+title+'" onclick="editFk(\''+refs[0]+'\');"></i> ';
			}else{
				ico+='<i class="fa fa-link" title="'+title+'" onclick="editFks(\''+refs.join(',')+'\');"></i> ';
			}
		}
		var pknull='';
		if (cDef.pk) pknull='PK';
		else if (cDef.nullable) pknull='Null';
		else pknull='Not Null';
		var temDefault='';
		if (cDef.default && cDef.default!='') temDefault='S';
		var trow=tr.clone();
		trow.attr('id', 'columnTr_'+col);
		trow.attr('class', 'pk');
		trow.attr('ondblclick', 'editColumn(\''+tableId+'\', \''+col+'\');');
		trow.find('td:eq(0)').html(ico);
		trow.find('td:eq(1)').html(col);
		trow.find('td:eq(2)').html(cDef.logical);
		trow.find('td:eq(3)').html(cDef.type);
		trow.find('td:eq(4)').html(pknull);
		trow.find('td:eq(5)').html(temDefault);
		trow.find('td:eq(6)').html('<i class="fa fa-trash-o simulink" onclick="removeColumn(\''+tableId+'\', \''+col+'\');"></i>');
		$('#columnsContainer table tbody').append(trow);
	}
	for(var col in tabDefs.columns){
		var cDef=tabDefs.columns[col];
		if (cDef.pk) continue;
		var isFk=false;
		var ico='';
		if (cDef.pk) ico+='<i class="fa fa-key"></i> ';
		if (cDef?.fkrefs && cDef.fkrefs.length>0) {
			isFk=true;
			var refs=[];
			var title='';
			for (var i=0; i<cDef.fkrefs.length; i++) {
				var frase='';
				var rel=relats[cDef.fkrefs[i]];
				for (var k=0; k<rel.columns.length; k++){
					if (rel.columns[k].child==col) title += (title==''?'':'\n') + rel.sourceTable + '.' + rel.columns[k].parent;
					if (rel.phrase) title+=' ('+rel.phrase+(rel.inverse?' - '+rel.inverse:'')+')';
				}
			}
			ico+='<i class="fa fa-link" title="'+title+'"></i> ';
		}
		var pknull='';
		if (cDef.pk) pknull='PK';
		else if (cDef.nullable) pknull='Null';
		else pknull='Not Null';
		var temDefault='';
		var valDefault='';
		if (cDef.default && cDef.default!='') {
			temDefault='S';
			valDefault=cDef.default;
		}
		var trow=tr.clone();
		trow.attr('id', 'columnTr_'+col);
		if (isFk) trow.attr('class', 'fk'); else trow.attr('class', '');
		trow.attr('ondblclick', 'editColumn(\''+tableId+'\', \''+col+'\');');
		trow.find('td:eq(0)').html(ico);
		trow.find('td:eq(1)').html(col);
		trow.find('td:eq(2)').html(cDef.logical);
		trow.find('td:eq(3)').html(cDef.type);
		trow.find('td:eq(4)').html(pknull);
		trow.find('td:eq(5)').html(temDefault);
		if (valDefault!='') trow.find('td:eq(5)').attr('title', valDefault); else trow.find('td:eq(5)').removeAttr('title');
		trow.find('td:eq(6)').html('<i class="fa fa-trash-o simulink" onclick="removeColumn(\''+tableId+'\', \''+col+'\');"></i>');
		$('#columnsContainer table tbody').append(trow);
	}
}

var editColumnHtml=null;
function editColumn(tableId, columnId){
	if ($('#editColumnDialog').length==0){
		var md=$('<div id="editColumnDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (editColumnHtml==null){
		$.ajax({
			url:'ajax_modelo.php',
			type:'get',
			dataType:'html',
			data:{m:'editColumn', file:modelFile, tableId:tableId, columnId:columnId},
			success:function(html){
				editColumnHtml=html;
				editColumn(tableId, columnId);
			}
		});
		return;
	}
	$('#editColumnDialog').html(editColumnHtml);
	fillEditColumnDialog(tableId, columnId);
	$('#editColumnDialog').dialog({
		modal:true,
		width:600,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText:'Fechar',
		title: 'Edição da coluna '+columnId,
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#editColumnDialog').dialog('close').remove();
		}
	});
}
function fillEditColumnDialog(tableId, columnId){
	var tabDefs=tbDefs[tableId];
	var colDefs=tabDefs.columns[columnId];
	var html=$('#editColumnDialog').html();
	html=html.replace(/__tableId__/g, tableId);
	html=html.replace(/__tableLogicalName__/g, tabDefs.logical);
	html=html.replace(/__columnId__/g, columnId);
	html=html.replace(/__colLogicalName__/g, colDefs.logical);
	if (colDefs.fk) {
		var fisicoTableRel=colDefs.fk.table;
		var fisicoColumnRel=colDefs.fk.column;
		var tbDefRel=tbDefs[fisicoTableRel];
		var relacionamento='<b>'+fisicoTableRel+'</b> ('+tbDefRel.logical+') - <b>'+fisicoColumnRel+'</b> ('+tbDefRel.columns[fisicoColumnRel].logical+')';
	} else relacionamento='';
	html=html.replace(/__relacionamento__/g, relacionamento);
	html=html.replace(/__size__/g, colDefs.size);
	html=html.replace(/__columnDefault__/g, (colDefs.default?colDefs.default:''));
	html=html.replace(/__phrase__/g, (colDefs.phrase?colDefs.phrase:''));
	html=html.replace(/__inverse__/g, (colDefs.inverse?colDefs.inverse:''));
	$('#editColumnDialog').html(html);
	$('#editColumnDialog').find('#divSize').css('display', $('#type_'+colDefs.type).attr('temSize')=='true'?'block':'none');
	if (colDefs.fk){
		$('#typeLabel').html('Tipo (FK)');
		$('#type').prop('disabled', true);
		$('#sequence').prop('disabled', true);
	}
	$('#editColumnDialog').find('#type').val(colDefs.type);
	$('#editColumnDialog').find('#type').val(colDefs.type);
	$('#editColumnDialog').find('#divRelacionamento').css('display', colDefs.fk?'block':'none');
	$('#editColumnDialog').find('#pk').prop('checked', colDefs.pk);
	$('#editColumnDialog').find('#sequence').prop('checked', colDefs.sequence);
	$('#editColumnDialog').find('#notnull').prop('checked', !colDefs.nullable);
}
function updateColumn(tableId, columnId){
	var colDefs=tbDefs[tableId].columns[columnId];
	var isPkOrig=colDefs.pk;
	var physical=$('#editColumnDialog').find('#columnId').val();
	colDefs.logical=$('#editColumnDialog').find('#colLogicalName').val();
	colDefs.type=$('#editColumnDialog').find('#type').val();
	colDefs.pk=$('#editColumnDialog').find('#pk').prop('checked');
	colDefs.sequence=$('#editColumnDialog').find('#sequence').prop('checked');
	if (colDefs.sequence==false) delete colDefs.sequence;
	colDefs.nullable=!$('#editColumnDialog').find('#notnull').prop('checked');
	var valDefault=$('#editColumnDialog').find('#columnDefault').val();
	if (valDefault=='' || valDefault=='undefined' || valDefault=='false') delete colDefs.default; else colDefs.default=valDefault;
	var size=$('#editColumnDialog').find('#size').val();
	if (size=='' || size=='undefined') delete colDefs.size; else colDefs.size=size;
	var phrase=$('#editColumnDialog').find('#phrase').val();
	var inverse=$('#editColumnDialog').find('#inverse').val();
	if (physical!=columnId) { // se alterou o nome fisico, precisa propagar para as relats onde se aplica
		tbDefs[tableId].columns[physical]=tbDefs[tableId].columns[columnId];
		delete tbDefs[tableId].columns[columnId];
		if (isPkOrig && colDefs.pk) { // se é pk e se manteve, alteração no nome da coluna precisa propagar para as relats onde se aplica
			for (var relId in relats){
				var relDef=relats[relId];
				if (relDef.sourceTable==tableId){
					for (var i=0; i<relDef.columns.length; i++){
						if (relDef.columns[i].parent==columnId){
							relDef.columns[i].parent=physical;
						}
					}
				}
				if (relDef.targetTable==tableId){
					for (var i=0; i<relDef.columns.length; i++){
						if (relDef.columns[i].child==columnId){
							relDef.columns[i].child=physical;
						}
					}
				}
			}
		}else if (isPkOrig) { // se era pk e não é mais, precisa remover da relats onde se aplica
			for (var relId in relats){
				var relDef=relats[relId];
				if (relDef.sourceTable==tableId){
					for (var i=0; i<relDef.columns.length; i++){
						if (relDef.columns[i].parent==columnId){
							relDef.columns.splice(i, 1);
							i--;
						}
					}
				}
				if (relDef.targetTable==tableId){
					for (var i=0; i<relDef.columns.length; i++){
						if (relDef.columns[i].child==columnId){
							relDef.columns.splice(i, 1);
							i--;
						}
					}
				}
			}
		}else if (colDefs.pk) { // se não era pk e agora é, precisa adicionar na relats onde se aplica
			for (var relId in relats){
				var relDef=relats[relId];
				if (relDef.sourceTable==tableId){
					relDef.columns.push({parent:physical, child:relDef.targetColumn});
				}
				if (relDef.targetTable==tableId){
					relDef.columns.push({parent:relDef.sourceColumn, child:physical});
				}
			}
		}
	}
	redrawTable(tableId, tbDefs[tableId]);
	drawFkLines();
	$('#editColumnDialog').dialog('close');
	editTable(tableId);

	// Check for model changes after column update
	checkForModelChanges();
}
function toggleMinimap(to) {
	if (typeof to=='undefined') to=!$('#minimap-wrapper').is(':visible');
	$('#minimap-wrapper').toggle(to);

	// Check for layout changes after minimap toggle
	checkForLayoutChanges();
}

// Initialize marquee selection
function initMarqueeSelection() {
	// Create marquee element if it doesn't exist
	if (!marqueeElement) {
		marqueeElement = document.createElement('div');
		marqueeElement.id = 'marquee-selection';
		marqueeElement.style.position = 'absolute';
		marqueeElement.style.border = '1px dashed #007bff';
		marqueeElement.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
		marqueeElement.style.pointerEvents = 'none';
		marqueeElement.style.display = 'none';
		marqueeElement.style.zIndex = '1000';
		modelContainer.appendChild(marqueeElement);
	}

	// Add event listeners for marquee selection
	$(modelContainer).on('mousedown', startMarqueeSelection);
	$(document).on('mousemove', updateMarqueeSelection);
	$(document).on('mouseup', endMarqueeSelection);
}

// Start marquee selection
function startMarqueeSelection(e) {
	// Only start marquee if left mouse button is pressed and not on a table
	if (e.button !== 0 || $(e.target).closest('.sttable').length > 0) {
		return;
	}
	
	// Prevent default behavior
	e.preventDefault();
	
	// Get mouse position relative to model container
	var containerOffset = $(modelContainer).offset();
	marqueeStartX = (e.pageX - containerOffset.left)/currentScale;
	marqueeStartY = (e.pageY - containerOffset.top)/currentScale;
	
	// Show and position marquee element
	marqueeElement.style.left = marqueeStartX + 'px';
	marqueeElement.style.top = marqueeStartY + 'px';
	marqueeElement.style.width = '0';
	marqueeElement.style.height = '0';
	marqueeElement.style.display = 'block';
	
	// Set marquee active flag
	isMarqueeActive = true;
}

// Update marquee selection
function updateMarqueeSelection(e) {
	if (!isMarqueeActive) {
		return;
	}
	
	// Get mouse position relative to model container
	var containerOffset = $(modelContainer).offset();
	var currentX = (e.pageX - containerOffset.left)/currentScale;
	var currentY = (e.pageY - containerOffset.top)/currentScale;
	
	// Calculate marquee dimensions
	var width = Math.abs(currentX - marqueeStartX);
	var height = Math.abs(currentY - marqueeStartY);
	var left = Math.min(currentX, marqueeStartX);
	var top = Math.min(currentY, marqueeStartY);
	
	// Update marquee element position and size
	marqueeElement.style.left = left + 'px';
	marqueeElement.style.top = top + 'px';
	marqueeElement.style.width = width + 'px';
	marqueeElement.style.height = height + 'px';
	
	// Auto-scroll if near edges
	var edgeThreshold = 20;
	var scrollSpeed = 10;
	
	if (e.clientY - $(modelContainer).offset().top < edgeThreshold) {
		modelContainer.scrollTop -= scrollSpeed;
	} else if (e.clientY - $(modelContainer).offset().top > $(modelContainer).height() - edgeThreshold) {
		modelContainer.scrollTop += scrollSpeed;
	}
	
	if (e.clientX - $(modelContainer).offset().left < edgeThreshold) {
		modelContainer.scrollLeft -= scrollSpeed;
	} else if (e.clientX - $(modelContainer).offset().left > $(modelContainer).width() - edgeThreshold) {
		modelContainer.scrollLeft += scrollSpeed;
	}
}

// End marquee selection
function endMarqueeSelection(e) {
	if (!isMarqueeActive) {
		return;
	}
	console.log('mouse up do marquee');
	
	// Hide marquee element
	marqueeElement.style.display = 'none';
	
	// Get marquee bounds
	var marqueeLeft = parseInt(marqueeElement.style.left);
	var marqueeTop = parseInt(marqueeElement.style.top);
	var marqueeRight = marqueeLeft + parseInt(marqueeElement.style.width);
	var marqueeBottom = marqueeTop + parseInt(marqueeElement.style.height);
	if (marqueeLeft==marqueeRight || marqueeTop==marqueeBottom) {
		isMarqueeActive = false;
		return;
	}
	
	// Find tables within marquee bounds
	var tablesInMarquee = [];
	$('.sttable').each(function() {
		var tableLeft = parseInt($(this).css('left'));
		var tableTop = parseInt($(this).css('top'));
		var tableRight = tableLeft + $(this).outerWidth();
		var tableBottom = tableTop + $(this).outerHeight();
		
		// Check if table is within marquee bounds
		if (tableLeft < marqueeRight && 
			tableRight > marqueeLeft && 
			tableTop < marqueeBottom && 
			tableBottom > marqueeTop) {
			tablesInMarquee.push(this.id);
		}
	});
	
	// Select tables within marquee bounds
	if (tablesInMarquee.length > 0) {
		// If Ctrl key is not pressed, clear current selection
		if (!e.ctrlKey) {
			$('.selected').removeClass('selected');
			selectedTable = [];
		}
		
		// Add tables to selection
		for (var i = 0; i < tablesInMarquee.length; i++) {
			selectTable(tablesInMarquee[i], true);
		}
	}

	// Reset marquee active flag via setTimeout to signal modelContainer click event
	setTimeout(function() { isMarqueeActive = false; }, 100); // Use setTimeout to signal modelContainer click event
}
function updateTable(tableId){
	var newTableId=$('#tableId').val();
	var logicalName=$('#logicalName').val();
	if (!newTableId){
		alert('Preencha o nome da tabela');
		return;
	}
	if (!logicalName){
		alert('Preencha o nome lógico da tabela');
		return;
	}
	if (newTableId!=tableId){
		if (tbDefs[newTableId]){
			alert('Já existe uma tabela com este nome');
			return;
		}
		tbDefs[newTableId]=tbDefs[tableId];
		delete tbDefs[tableId];
		tbPositions[newTableId]=tbPositions[tableId];
		delete tbPositions[tableId];
		// identificar se tem alguma relação com a tabela antiga e atualizar
		for (var relId in relats) {
			var relDef = relats[relId];
			if (relDef.sourceTable==tableId) {
				relDef.sourceTable=newTableId;
			}
			if (relDef.targetTable==tableId) {
				relDef.targetTable=newTableId;
			}
			relats[relId]=relDef;
		}
		$('#'+tableId+'_logical').remove();
		$('#'+tableId+'_physical').remove();
		tbDefs[newTableId].logical=logicalName;
		addTableToSidebar(newTableId);
	}else{
		tbDefs[tableId].logical=logicalName;
		$('#'+tableId+'_logical').html(logicalName);
	}
	editTable(newTableId);
	drawTables();
	drawFkLines();
	saveModel();
}
function addColumn(tableId, logical=null){
	var valTableId = $('#editTableDialog').find('#tableId').val();
	if (valTableId!=tableId) {
		updateTable(tableId);
		addColumn(valTableId, logical);
		return;
	}
	var data={m:'addColumn', file:modelFile, tableId:tableId};
	if (logical) data.logical=logical;
	$.ajax({
		url:'ajax_modelo.php',
		type:'get',
		dataType:'html',
		data:data,
		success:function(html){
			if ($('#addColumnDialog').length==0){
				var md=$('<div id="addColumnDialog" style="display:none;"></div>');
				md.appendTo('body');
			}
			$('#addColumnDialog').html(html);
			$('#addColumnDialog').dialog({
				modal:true,
				width:600,
				position: {
					my: 'right top',
					at: 'right-40 top+80'
				},
				closeText:'Fechar',
				title: 'Adicionar coluna à tabela '+tableId,
				dialogClass: 'default',
				close: function() {
					$('#addColumnDialog').dialog('close').remove();
				}
			});
		}
	});
}
function addColumn_go(tableId){
	var columnId=$('#columnId').val();
	var colLogicalName=$('#colLogicalName').val();
	var type=$('#type').val();
	var size=$('#size').val();
	var pk=$('#pk').is(':checked');
	var sequence=$('#sequence').is(':checked');
	var notnull=$('#notnull').is(':checked');
	if (!columnId){
		alert('Preencha o nome da coluna');
		return;
	}
	if (!colLogicalName){
		alert('Preencha o nome lógico da coluna');
		return;
	}
	if (!type){
		alert('Selecione o tipo da coluna');
		return;
	}
	if (type=='varchar' && !size){
		alert('Preencha o tamanho da coluna');
		return;
	}
	if ($('#type_'+type).attr('temSize')=='true' && (size!=-1 && !size>0)){
		alert('O tamanho da coluna deve ser maior que zero ou -1, para sem limite');
		return;
	}
	tbDefs[tableId].columns[columnId]={logical:colLogicalName, type:type, size:size, pk:pk, sequence:sequence, nullable:!notnull};
	$('#addColumnDialog').dialog('close').remove();
	editTable(tableId);
	saveModel();
	// decidir se vamos mandar via ajax ou manter local e só persistir quando salvar o modelo

	// Check for model changes after adding column
	checkForModelChanges();
}
function removeColumn(tableId, columnId){
	if (!confirm('Tem certeza que deseja remover a coluna '+columnId+'?')) return;
	delete tbDefs[tableId].columns[columnId];
	editTable(tableId);

	// Check for model changes after removing column
	checkForModelChanges();
}
var onCreateRel=false;
var createdRel={};
function startRelationshipSelection(type){
	onCreateRel=true;
	createdRel={};
	createdRel.type=type;
	createdRel.from=null;
	createdRel.to=null;
	$('#divModelo').addClass('relParent');
}
var relationshipDialogHtml=null;
function createRelationship(){
	if (!createdRel.from || !createdRel.to) return;
	if ($('#relationshipDialog').length==0){
		var md=$('<div id="relationshipDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (relationshipDialogHtml==null){
		$.ajax({
			url:'ajax_modelo.php',
			type:'get',
			dataType:'html',
			data:{m:'getRelationshipDialog', file:modelFile},
			success:function(html){
				relationshipDialogHtml=html;
				createRelationship();
			}
		});
		return;
	}
	$('#relationshipDialog').html(relationshipDialogHtml);
	fillCreateRelationshipDialog();
	$('#relationshipDialog').dialog({
		modal:true,
		width:700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText:'Fechar',
		title: 'Criar relacionamento',
		dialogClass: 'default',
		close: function() {
			$('#relationshipDialog').dialog('close').remove();
		}
	});
}
function fillCreateRelationshipDialog(){
	var html=$('#relationshipDialog').html();
	fromDef=tbDefs[createdRel.from];
	toDef=tbDefs[createdRel.to];
	var pksFrom={};
	var pkColsFrom=[];
	for(var col in fromDef.columns){
		if (fromDef.columns[col].pk) {
			pksFrom[col]=fromDef.columns[col];
			pkColsFrom.push(col);
		}
	}
	var relId=createdRel.from+'_'+createdRel.to+'_'+pkColsFrom.join('_')+'_FK';



	html = html.replace(/__relName__/g, relId);
	html=html.replace(/__relfromLogical__/g, fromDef.logical);
	html=html.replace(/__relfrom__/g, createdRel.from);
	html=html.replace(/__relto__/g, createdRel.to);
	html=html.replace(/__reltoLogical__/g, toDef.logical);
	html=html.replace(/__phrase__/g, '');
	html=html.replace(/__inverse__/g, '');
	html=html.replace(/__btnCreateRelClick__/g, 'createRelationship_go();');
	$('#relationshipDialog').html(html);
	var pksFrom={};
	var pksDescr=[];
	for(var col in fromDef.columns){
		if (fromDef.columns[col].pk) {
			pksFrom[col]=fromDef.columns[col];
			pksDescr.push(col);
		}
	}
	for (var col in pksFrom){
		var msg='';
		var colDef=pksFrom[col];
		// verificar se já existe a coluna no destino
		if (toDef.columns[col]) {
			msg='Já existe a coluna no destino. Será aproveitada se o nome físico for mantido.';
			$('#relationshipDialog').find('#distinctiveMsg_'+col).show();
		}
		var divRelToCols=$('#divRelToCols').clone();
		var colHtml=divRelToCols.html();
		colHtml=colHtml.replace(/"distinctiveMsg"/g, '"distinctiveMsg_'+col+'"');
		colHtml=colHtml.replace(/__tocolLogical__/g, pksFrom[col].logical);
		colHtml=colHtml.replace(/__tocolPhysical__/g, col);
		colHtml=colHtml.replace(/__relcolfromLogical__/g, pksFrom[col].logical);
		colHtml=colHtml.replace(/__relcolFromPhysical__/g, col);
		colHtml=colHtml.replace(/"tocolLogical"/g, '"tocolLogical_'+col+'"');
		colHtml=colHtml.replace(/"tocolPhysical"/g, '"tocolPhysical_'+col+'"');
		colHtml=colHtml.replace(/__relfrom__/g, createdRel.from);
		colHtml=colHtml.replace(/__relto__/g, createdRel.to);

		var fromDescr=(colDef.pk?'PK':'');
		fromDescr+=(colDef.fk?(fromDescr==''?'':', ')+'FK':'');
		for (var i=0; i<fromDef.constraints.length; i++) {
			if (fromDef.constraints[i].type=='unique' && fromDef.constraints[i].columns.includes(sourceCol)) {
				fromDescr+=(fromDescr==''?'':', ')+'Unique';
				break;
			}
		}
		fromDescr+=', '+(colDef.nullable?'NULL':'NOT NULL');
		fromDescr+=', '+colDef.type+(colDef?.size && colDef.size!=''?'('+colDef.size+')':'');
		colHtml=colHtml.replace(/__relFromColDescr__/g, fromDescr);
		divRelToCols.html(colHtml);
		$('#portaRelToCols').append(divRelToCols);
		divRelToCols.show();
		if (msg!=''){
			$('#distinctiveMsg_'+col).html(msg);
			$('#distinctiveMsg_'+col).show();
		}
	}
	html=$('#relationshipDialog').html();
	$('#relationshipDialog').html(html);
	switch(createdRel.type){
		case 'id':
			$('#relationshipDialog').find('#reltype').val(0);
			$('#relationshipDialog').find('#cardinality').val('0m');
			break;
		case 'nonidmand':
			$('#relationshipDialog').find('#reltype').val(1);
			$('#relationshipDialog').find('#cardinality').val('0m');
			break;
		case 'nonidopt':
			$('#relationshipDialog').find('#reltype').val(3);
			$('#relationshipDialog').find('#cardinality').val('0o');
			break;
		case 'onetoone':
			$('#relationshipDialog').find('#reltype').val(1);
			$('#relationshipDialog').find('#cardinality').val('2m');
			break;
	}
	// var hidden=$('<input type="hidden" name="relId" value="'+relId+'"/>');
	// $('#relDivsWrapper').append(hidden);
	$('#btnCreateRel').html('Criar Relacionamento');
}
function updateRelName(fromPhys, toPhys) {
	var relName=$('#relationshipDialog').find('#relName').val();
	var toCols=[];
	$('#relationshipDialog #relDivsWrapper').find('.relcolphysical').each(function() {
		if ($(this).attr('id')=='tocolPhysical') return;
		toCols.push($(this).val());
	});
	if (toCols.length==0) return;
	toCols.sort();
	var tocolPhysical=toCols.join('_');
	var name=fromPhys+'_'+toPhys+'_'+tocolPhysical+'_FK';
	$('#relationshipDialog').find('#relName').val(name);
}
function createRelationship_go(){
	var relId=$('#relationshipDialog').find('#relName').val();
	for (var id in relats) {
		if (id.toUpperCase()==relId.toUpperCase()) {
			alert('Já existe um relacionamento com este nome. Verifique o nome, nomes físicos dos campos de destino e frases.');
			return;
		}
	}
	// debugger;
	var from=createdRel.from;
	var to=createdRel.to;
	var tbDefTo=tbDefs[to];
	var tbDefFrom=tbDefs[from];
	var phrase=$('#newrelphrase').val();
	var inverse=$('#newrelinverse').val();
	var reltype=$('#reltype').val();
	var cardinality=$('#cardinality').val().replace('m', '').replace('o', '');
	var cols=[];
	var colNames=[];
	var parentColumns=[];
	var propagaColsPk=[];
	$('.relcollogical').each(function() {
		var colId=$(this).attr('id').replace('tocolLogical_', '');
		if (colId=='tocolLogical') return;
		var colLogical=$(this).val();
		var colPhysical=$('#tocolPhysical_'+colId).val();
		var def={logical:colLogical, physical:colPhysical,type:tbDefFrom.columns[colId].type};
		if(tbDefFrom.columns[colId].size) def.size=tbDefFrom.columns[colId].size;
		def.fkrefs=[relId];
		switch(createdRel.type){
			case 'id':
				def.pk=true;
				def.nullable=false;
				def.sequence=false;
				propagaColsPk.push(colId);
				break;
			case 'nonidmand':
				def.pk=false;
				def.nullable=false;
				def.sequence=false;
				break;
			case 'nonidopt':
				def.pk=false;
				def.nullable=true;
				def.sequence=false;
				break;
			case 'onetoone':
				def.pk=false;
				def.nullable=true;
				def.sequence=false;
				break;
		}
		var reuseColumn=false;
		if (tbDefTo.columns[colPhysical]) {
			if (confirm('A coluna '+colPhysical+' já existe na tabela '+to+'. Deseja reutilizá-la?')) {
				reuseColumn=true;
			} else {
				return;
			}
		}
		if (phrase.trim()!='') def.phrase=phrase;
		if (inverse.trim()!='') def.inverse=inverse;
		colNames.push(colPhysical);
		parentColumns.push(colId);
		cols.push(def);
	});
	if (cols.length==0) return;
	
	// add columns to target table
	for (var k = 0; k < cols.length; k++) {
		tbDefTo.columns[cols[k].physical] = cols[k];
	}
	
	var relatColumns= [];
	for (var k = 0; k < cols.length; k++) {
		relatColumns.push({parent: parentColumns[k], child: colNames[k]});
	}

	if (!tbDefTo.parents) tbDefTo.parents = [];
	tbDefTo.parents.push(relId);
	
	if (!tbDefFrom.children) tbDefFrom.children = [];
	tbDefFrom.children.push(relId);
	tbDefs[from] = tbDefFrom;
	tbDefs[to] = tbDefTo;
	
	// Store relationship in relats object
	if (!window.relats) window.relats = {};
	
	relats[relId] = {
		sourceTable: from,
		targetTable: to,
		columns: relatColumns,
		cardinality: cardinality,
		reltype: reltype
	};
	
	if (phrase.trim() != '') relats[relId].phrase = phrase;
	if (inverse.trim() != '') relats[relId].inverse = inverse;
	
	if (propagaColsPk.length>0) {
		for (var aRelId in relats) {
			if (relats[aRelId].sourceTable!=to) continue;
			propagaColunasPk(aRelId);
		}
	}
	
	$('#relationshipDialog').dialog('close').remove();
	onCreateRel = false;
	$('#divModelo').removeClass('relParent');
	createdRel = null;
	drawTables();
	drawFkLines();
	saveModel();
}
function propagaColunasPk(theRelId){
	var relDef=relats[theRelId];
	var sourceTable=relDef.sourceTable;
	var targetTable=relDef.targetTable;
	var cols=[];
	var parentTbDef=tbDefs[sourceTable];
	// get all pk columns from source table
	for (var colId in parentTbDef.columns){
		if (parentTbDef.columns[colId].pk) cols.push(colId);
	}
	for (var i=0; i<cols.length; i++){
		var colId=cols[i];
		var colDef=tbDefs[sourceTable].columns[colId];
		if (colDef.pk){
			if (!tbDefs[targetTable].columns[colId]) {
				tbDefs[targetTable].columns[colId]={logical:colDef.logical, type:colDef.type, pk:(relDef.reltype==0), nullable:(relDef.reltype!=0)};
				if (colDef.size) tbDefs[targetTable].columns[colId].size=colDef.size;
			}else{
				tbDefs[targetTable].columns[colId].nullable=(relDef.reltype!=0);
			}
			if (relDef.reltype==0) {
				tbDefs[targetTable].columns[colId].pk=true;
				for (var aRelId in relats) {
					if (relats[aRelId].sourceTable!=targetTable) continue;
					propagaColunasPk(aRelId);
				}
			}
			if (!tbDefs[targetTable].columns[colId].fkrefs) tbDefs[targetTable].columns[colId].fkrefs=[];
			if (!tbDefs[targetTable].columns[colId].fkrefs.includes(theRelId)) tbDefs[targetTable].columns[colId].fkrefs.push(theRelId);
		}
	}
}

function editFks(keys){
	if ($('#selRelationshipDialog').length == 0) {
		var md = $('<div id="selRelationshipDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	var h='<div class="content container-fluid" style="font-size:0.9em;">';
	h+='<div class="row">';
	h+='<div class="col-sm-12" style="margin-bottom: 10px;">';
	h+='<b>Selecione o relacionamento a ser editado:</b>';
	h+='</div></div>';
	for (var i=0; i<keys.length; i++){
		var relId=keys[i];
		var relDef=relats[relId];
		var fromDef=tbDefs[relDef.sourceTable];
		var toDef=tbDefs[relDef.targetTable];
		var fromDescr='';
		var toDescr='';
		for (var j=0; j<relDef.columns.length; j++){
			fromDescr+=(fromDescr==''?'':', ')+fromDef.columns[relDef.columns[j].parent].logical;
			toDescr+=(toDescr==''?'':', ')+toDef.columns[relDef.columns[j].child].logical;
		}
		h+='<div class="row" style="margin-bottom: 10px;border-top: solid 1px lightgray;padding-top: 10px;">';
		h+='<div class="col-sm-12">';
		h+='<button type="button" class="btn btn-default btn-sm" onclick="editFk(\''+relId+'\');">Editar</button> ';
		h+=relDef.sourceTable+' ('+fromDef.logical+') =&gt; '+relDef.targetTable+' ('+toDef.logical+')';
		if (relDef.phrase) h+=' <b>'+relDef.phrase+' / '+relDef.inverse+'</b>';
		h+='</div></div>';
	}
	h+='</div>';
	$('#selRelationshipDialog').html(h);
	$('#selRelationshipDialog').dialog({
		modal: true,
		width: 700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText: 'Fechar',
		title: 'Selecione o relacionamento',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#selRelationshipDialog').dialog('close').remove();
		}
	});
}
// function editFk(sourceTable, sourceCol, targetTable, targetCol) {
function editFk(relId) {
	if ($('#relationshipDialog').length == 0) {
		var md = $('<div id="relationshipDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (relationshipDialogHtml == null) {
		$.ajax({
			url: 'ajax_modelo.php',
			type: 'get',
			dataType: 'html',
			data: { m: 'getRelationshipDialog', file: modelFile },
			success: function(html) {
				relationshipDialogHtml = html;
				editFk(relId);
			}
		});
		return;
	}
	$('#relationshipDialog').html(relationshipDialogHtml);
	fillRelationshipDialog(relId);
	$('#relationshipDialog').dialog({
		modal: true,
		width: 700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText: 'Fechar',
		title: 'Editar relacionamento',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#relationshipDialog').dialog('close').remove();
		}
	});
}
function fillRelationshipDialog(relId) {
	var html = $('#relationshipDialog').html();
	var relDef=relats[relId];
	var fromDef = tbDefs[relDef.sourceTable];
	var toDef = tbDefs[relDef.targetTable];
	// for each column in relDef.columns, get the column definition from fromDef and toDef and fill a copy of the html of divRelToCols
	var divRelToCols=$('#divRelToCols').clone();
	var divsDasCols=[];
	for (var i=0; i<relDef.columns.length; i++) {
		var parentCol=relDef.columns[i].parent;
		var childCol=relDef.columns[i].child;
		var colFromDef=fromDef.columns[relDef.columns[i].parent];
		var colToDef=toDef.columns[relDef.columns[i].child];
		var colHtml=divRelToCols.html();

		var fromDescr=(colFromDef.pk?'PK':'');
		fromDescr+=(colFromDef.fk?(fromDescr==''?'':', ')+'FK':'');
		for (var j=0; j<fromDef.constraints.length; j++) {
			if (fromDef.constraints[j].type=='unique' && fromDef.constraints[j].columns.includes(sourceCol)) {
				fromDescr+=(fromDescr==''?'':', ')+'Unique';
				break;
			}
		}
		fromDescr+=', '+(colFromDef.nullable?'NULL':'NOT NULL');
		fromDescr+=', '+colFromDef.type+(colFromDef?.size && colFromDef.size!=''?'('+colFromDef.size+')':'');

		var toDescr=(colToDef.pk?'PK':'');
		toDescr+=(colToDef.fk?(toDescr==''?'':', ')+'FK':'');
		for (var j=0; j<toDef.constraints.length; j++) {
			if (toDef.constraints[j].type=='unique' && toDef.constraints[j].columns.includes(childCol)) {
				toDescr+=(toDescr==''?'':', ')+'Unique';
				break;
			}
		}
		toDescr+=', '+(colToDef.nullable?'NULL':'NOT NULL');
		toDescr+=', '+colToDef.type+(colToDef?.size && colToDef.size!=''?'('+colToDef.size+')':'');

		colHtml=colHtml.replace(/__tocolLogical__/g, colToDef.logical);
		colHtml=colHtml.replace(/__tocolPhysical__/g, colToDef.physical);
		colHtml=colHtml.replace(/__relcolFromLogical__/g, colFromDef.logical);
		colHtml=colHtml.replace(/"tocolLogical"/g, '"tocolLogical_'+parentCol+'"');
		colHtml=colHtml.replace(/"tocolPhysical"/g, '"tocolPhysical_'+parentCol+'"');
		colHtml=colHtml.replace(/__relFromColDescr__/g, fromDescr);
		colHtml=colHtml.replace(/__relToColDescr__/g, toDescr);
		colHtml=colHtml.replace(/__relcolFromPhysical__/g, colFromDef.physical);
		colHtml=colHtml.replace(/__relcolfromLogical__/g, colFromDef.logical);
		colHtml=colHtml.replace(/__relcoltoLogical__/g, colToDef.logical);
		colHtml=colHtml.replaceAll(/__relfrom__/g, relDef.sourceTable);
		colHtml=colHtml.replaceAll(/__relto__/g, relDef.targetTable);
		// divRelToCols.html(colHtml);
		var divDaCol=$('<div id="divRelToCols'+parentCol+'"></div>');
		divDaCol.html(colHtml);
		divsDasCols.push(divDaCol);
	}





	html = html.replace(/__relName__/g, relId);
	html = html.replace(/__relfromLogical__/g, fromDef.logical);
	html = html.replace(/__relfrom__/g, relDef.sourceTable);
	html = html.replace(/__reltoLogical__/g, toDef.logical);
	html = html.replace(/__relto__/g, relDef.targetTable);

	html = html.replace(/__btnCreateRelClick__/g, 'updateRelationship(\''+relId+'\');');
	html = html.replace(/__btnDeleteRelClick__/g, 'deleteRelationship(\''+relId+'\');');

	// Set phrase and inverse if they exist on the relationship definition
	var phrase = relats[relId].phrase || '';
	var inverse = relats[relId].inverse || '';
	html = html.replace(/__phrase__/g, phrase);
	html = html.replace(/__inverse__/g, inverse);
	
	$('#relationshipDialog').html(html);
	$('#relationshipDialog').find('#reltype').val(relats[relId].reltype);
	var cardSuffix = relats[relId].reltype<2?'m':'o';
	$('#relationshipDialog').find('#cardinality').val(relats[relId].cardinality+cardSuffix);
	$('#relationshipDialog').find('#portaRelToCols').append(divsDasCols);
	
	// Show the column selection div if needed
	// $('#divRelToCols').show();
	$('#btnDeleteRel').show();
	
	$('#btnCreateRel').html('Atualizar Relacionamento');
}


function updateRelationship(relId) {
	var relDef = relats[relId];
	var targetTable = relDef.targetTable;
	var phrase = $('#relationshipDialog').find('#newrelphrase').val();
	var inverse = $('#relationshipDialog').find('#newrelinverse').val();
	var reltype = $('#relationshipDialog').find('#reltype').val();
	var cardinality = $('#relationshipDialog').find('#cardinality').val().replace('m', '').replace('o', '');
	// fazer os testes de consistência e duplicação antes de alterar as definições
	for (var i=0; i<relDef.columns.length; i++) {
		var parentCol = relDef.columns[i].parent;
		var childCol = relDef.columns[i].child;
		var logical = $('#relationshipDialog').find('#tocolLogical_'+parentCol).val();
		var physical = $('#relationshipDialog').find('#tocolPhysical_'+parentCol).val();
		if (logical.trim()=='') {
			alert('Preencha o nome lógico da coluna filha da '+parentCol);
			return;
		}
		if (physical.trim()=='') {
			alert('Preencha o nome físico da coluna filha da '+parentCol);
			return;
		}
		if (physical!=childCol) {
			if (tbDefs[targetTable].columns[physical]) {
				alert('A coluna '+physical+' já existe na tabela '+targetTable);
				return;
			}
		}
	}

	var msgs=[];
	for (var i=0; i<relDef.columns.length; i++) {
		var parentCol = relDef.columns[i].parent;
		var childCol = relDef.columns[i].child;
		var logical = $('#relationshipDialog').find('#tocolLogical_'+parentCol).val();
		var physical = $('#relationshipDialog').find('#tocolPhysical_'+parentCol).val();
		// se mudou o nome físico da coluna...
		if (physical!=childCol) {
			tbDefs[targetTable].columns[physical]=tbDefs[targetTable].columns[childCol];
			tbDefs[targetTable].columns[physical].physical=physical;
			delete tbDefs[targetTable].columns[childCol];
			// verificar se há alguma relação com a coluna antiga como parent e atualizar
			for (var j=0; j<tbDefs[targetTable].columns[physical].fkrefs.length; j++) {
				var aRelId = tbDefs[targetTable].columns[physical].fkrefs[j];
				if (aRelId==relId) continue; 
				var aRelDef = relats[aRelId];
				for (var k=0; k<aRelDef.columns.length; k++){
					if (aRelDef.columns[k].parent==childCol) {
						aRelDef.columns[k].parent=physical;
						msgs.push('A coluna '+childCol+' foi renomeada para '+physical +' e também é filha da relação '+aRelId + '. Verifique se é necessário atualizar a relação.');
						break;
					}
				}
				relats[aRelId]=aRelDef;
			}
			// verificar se há alguma relação com a target table como source e a coluna antiga como parent e atualizar
			for (var aRelId in relats) {
				if (aRelId==relId) continue; // não atualizar a relação atual
				if (relats[aRelId].sourceTable!=targetTable) continue;
				var aRelDef = relats[aRelId];
				for (var k=0; k<aRelDef.columns.length; k++){
					if (aRelDef.columns[k].parent==childCol) {
						aRelDef.columns[k].parent=physical;
						msgs.push('A coluna '+childCol+' foi renomeada para '+physical +' e é propagada na relação '+aRelId + '. Verifique se é necessário atualizar a relação.');
						break;
					}
				}
				relats[aRelId]=aRelDef;
			}

			// verificar se há unique keys com a coluna atual
			if (tbDefs[targetTable].constraints) {
				for (var uniqueName in tbDefs[targetTable].constraints) {
					if (tbDefs[targetTable].constraints[uniqueName].type!='unique') continue;
					for (var i=0; i<tbDefs[targetTable].constraints[uniqueName].columns.length; i++){
						if (tbDefs[targetTable].constraints[uniqueName].columns[i]==childCol) {
							tbDefs[targetTable].constraints[uniqueName].columns[i]=physical;
							break;
						}
					}
				}
			}
			relDef.columns[i].child=physical;
		}
		if (logical!=tbDefs[targetTable].columns[physical].logical) {
			tbDefs[targetTable].columns[physical].logical=logical;
		}
	}
	if (phrase.trim()!='') {
		relDef.phrase = phrase;
	} else {
		delete relDef.phrase;
	}
	if (inverse.trim()!='') {
		relDef.inverse = inverse;
	} else {
		delete relDef.inverse;
	}
	relDef.cardinality = cardinality;
	relDef.reltype = reltype;
	relats[relId] = relDef;
	if (msgs.length>0) {
		alert(msgs.join('\n'));
	}	
	$('#relationshipDialog').dialog('close');
	drawTables();
	drawFkLines();
}
function deleteRelationship(relId) {
	if (!confirm('Tem certeza que deseja excluir o relacionamento?')) return;
	var removeTargetColumn=false;
	var relDef = relats[relId];
	var targetColumns=[];
	for (var i=0; i<relDef.columns.length; i++){
		targetColumns.push(relDef.columns[i].child);
	}
	var msgConfirm='Excluir a coluna '+targetColumns[0]+' da tabela filha '+ relDef.targetTable+'?';
	if (targetColumns.length>1) {
		msgConfirm='Excluir as colunas '+targetColumns.join(', ')+' da tabela filha '+ relDef.targetTable+'?';
	}
	if (confirm(msgConfirm)) removeTargetColumn=true;
	for (var i=0; i<relDef.columns.length; i++){
		var colDef = tbDefs[relDef.targetTable].columns[relDef.columns[i].child];
		var idx = colDef.fkrefs.indexOf(relId);
		if (idx!=-1) colDef.fkrefs.splice(idx, 1);
		if (removeTargetColumn) {
			delete tbDefs[relDef.targetTable].columns[relDef.columns[i].child];
		}
	}
	var idx = tbDefs[relDef.targetTable].parents.indexOf(relId);
	if (idx!=-1) tbDefs[relDef.targetTable].parents.splice(idx, 1);
	idx = tbDefs[relDef.sourceTable].children.indexOf(relId);
	if (idx!=-1) tbDefs[relDef.sourceTable].children.splice(idx, 1);
	delete relats[relId];
	$('#relationshipDialog').dialog('close');
	drawTables();
	drawFkLines();
}
var uniqueListDialogHtml=null;
function editUniques(tableId){
	$('#editTableDialog').dialog('close').remove();
	if ($('#uniqueListDialog').length==0){
		var md=$('<div id="uniqueListDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (uniqueListDialogHtml == null) {
		$.ajax({
			url: 'ajax_modelo.php',
			type: 'get',
			dataType: 'html',
			data: { m: 'getuniqueListDialogHtml', file: modelFile },
			success: function(html) {
				uniqueListDialogHtml = html;
				editUniques(tableId);
			}
		});
		return;
	}
	$('#uniqueListDialog').html(uniqueListDialogHtml);
	fillEditUniqueListDialog(tableId);
	$('#uniqueListDialog').dialog({
		modal: true,
		width: 700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText: 'Fechar',
		title: 'Editar chaves únicas',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#uniqueListDialog').dialog('close').remove();
			editTable(tableId);
		}
	});
}
function fillEditUniqueListDialog(tableId){
	var html=$('#uniqueListDialog').html();
	var tbDef=tbDefs[tableId];
	html=html.replace(/__tableLogicalName__/g, tbDef.logical);
	html=html.replace(/__tableId__/g, tableId);
	$('#uniqueListDialog').html(html);
	var uniques=tbDef.constraints || {};
	for(var uniqueName in uniques){
		if (uniques[uniqueName].type!='unique') continue;
		var templateTr=$('#uniquesContainer #templateUniqueTr').clone();
		var uniqueHtml=templateTr.html();
		uniqueHtml=uniqueHtml.replace(/__uniqueName__/g, uniqueName);
		uniqueHtml=uniqueHtml.replace(/__uniqueColumns__/g, uniques[uniqueName].columns.join(', '));
		templateTr.html(uniqueHtml);
		templateTr.attr('id', 'uniqueTr_'+uniqueName);
		templateTr.show();
		$('#uniquesContainer table tbody').append(templateTr);
	}
}
function editUnique(tableId, uniqueName){

	if ($('#uniqueDialog').length == 0) {
		var md = $('<div id="uniqueDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (uniqueDialogHtml == null) {
		$.ajax({
			url: 'ajax_modelo.php',
			type: 'get',
			dataType: 'html',
			data: { m: 'getUniqueDialog', file: modelFile },
			success: function(html) {
				uniqueDialogHtml = html;
				editUnique(tableId, uniqueName);
			}
		});
		return;
	}
	$('#uniqueDialog').html(uniqueDialogHtml);
	fillEditUniqueDialog(tableId, uniqueName);
	$('#uniqueDialog').dialog({
		modal: true,
		width: 700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText: 'Fechar',
		title: 'Editar chave única',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#uniqueDialog').dialog('close').remove();
		}
	});
}
function removeUnique(tableId, uniqueName){
	if (!confirm('Tem certeza que deseja remover a chave única '+uniqueName+'?')) return;
	delete tbDefs[tableId].constraints[uniqueName];
	$('#uniqueTr_'+uniqueName).remove();
}
function fillEditUniqueDialog(tableId, uniqueName){
	var html=$('#uniqueDialog').html();
	var tbDef=tbDefs[tableId];
	var ctDef=tbDef.constraints[uniqueName];
	html=html.replace(/__tableLogicalName__/g, tbDef.logical);
	html=html.replace(/__tableId__/g, tableId);
	html=html.replace(/__uniqueName__/g, uniqueName);
	html=html.replace(/__btnUniqueClick__/g, 'updateUnique(\''+tableId+'\', \''+uniqueName+'\');');
	html=html.replace(/__btnUniqueText__/g, 'Atualizar');
	$('#uniqueDialog').html(html);
	for(var colId in tbDef.columns){
		var templateTr=$('#uniqueColumnsContainer #templateUniqueTrCols').clone();
		var colHtml=templateTr.html();
		var colDef=tbDef.columns[colId];
		colHtml=colHtml.replace(/__columnId__/g, colId);
		colHtml=colHtml.replace(/__columnLogical__/g, colDef.logical);
		colHtml=colHtml.replace(/__columnDefs__/g, colDef.type);
		colHtml=colHtml.replace(/__tableId__/g, tableId);
		templateTr.html(colHtml);
		templateTr.attr('id', 'uniqueTr_'+colId);
		templateTr.show();
		$('#uniqueColumnsContainer table tbody').append(templateTr);
	}
	$('#uniqueKeyName').val(uniqueName);
	for(var k=0; k<ctDef.columns.length; k++){
		var colId=ctDef.columns[k];
		$('#cb-'+colId).prop('checked', true);
	}
}

var uniqueDialogHtml=null;
function addUnique(tableId){
	if ($('#uniqueDialog').length == 0) {
		var md = $('<div id="uniqueDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (uniqueDialogHtml == null) {
		$.ajax({
			url: 'ajax_modelo.php',
			type: 'get',
			dataType: 'html',
			data: { m: 'getUniqueDialog', file: modelFile },
			success: function(html) {
				uniqueDialogHtml = html;
				addUnique(tableId);
			}
		});
		return;
	}
	$('#uniqueDialog').html(uniqueDialogHtml);
	fillAddUniqueDialog(tableId);
	$('#uniqueDialog').dialog({
		modal: true,
		width: 700,
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText: 'Fechar',
		title: 'Adicionar chave única',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#uniqueDialog').dialog('close').remove();
		}
	});
}
function fillAddUniqueDialog(tableId){
	var html=$('#uniqueDialog').html();
	var tbDef=tbDefs[tableId];
	html=html.replace(/__tableLogicalName__/g, tbDef.logical);
	html=html.replace(/__tableId__/g, tableId);
	html=html.replace(/__btnUniqueClick__/g, 'addUnique_go(\''+tableId+'\');');
	html=html.replace(/__btnUniqueText__/g, 'Adicionar');
	$('#uniqueDialog').html(html);
	for(var colId in tbDef.columns){
		var templateTr=$('#uniqueColumnsContainer #templateUniqueTrCols').clone();
		var colHtml=templateTr.html();
		var colDef=tbDef.columns[colId];
		colHtml=colHtml.replace(/__columnId__/g, colId);
		colHtml=colHtml.replace(/__columnLogical__/g, colDef.logical);
		colHtml=colHtml.replace(/__columnDefs__/g, colDef.type);
		colHtml=colHtml.replace(/__tableId__/g, tableId);
		templateTr.html(colHtml);
		templateTr.attr('id', 'uniqueTr_'+colId);
		templateTr.show();
		$('#uniqueColumnsContainer table tbody').append(templateTr);
	}
	$('#uniqueKeyName').val('');
}
function updateNameUnique(tableId){
	var name=$('#uniqueKeyName').val();
	var suggestedName=$('#uniqueKeyNameSuggested').html();
	// if (name!=suggestedName) return;
	var cols=[];
	$('#uniqueColumnsContainer input[type="checkbox"]:checked').each(function(){
		cols.push($(this).attr('id').replace('cb-', ''));
	});
	if (cols.length==0) {
		$('#uniqueKeyNameSuggested').html('');
		if (name==suggestedName) $('#uniqueKeyName').val('');
		return;
	}
	cols.sort();
	var newName=tableId+'_'+name+cols.join('_') + '_UK';
	if (name==suggestedName) $('#uniqueKeyName').val(newName);
	$('#uniqueKeyNameSuggested').html(newName);
}
function addUnique_go(tableId){
	var name=$('#uniqueKeyName').val();
	var cols=[];
	$('#uniqueColumnsContainer input[type="checkbox"]:checked').each(function(){
		cols.push($(this).attr('id').replace('cb-', ''));
	});
	if (cols.length==0) {
		alert('Selecione pelo menos uma coluna');
		return;
	}
	if (name=='') {
		alert('Informe um nome para a chave única');
		return;
	}
	if (Array.isArray(tbDefs[tableId].constraints)) {
		tbDefs[tableId].constraints={};
	}
	tbDefs[tableId].constraints[name]={type:'unique', columns:cols};
	$('#uniqueDialog').dialog('close').remove();
	redrawTable(tableId);
	editUniques(tableId);
	saveModel();
}
var saveModelDialogHtml=null;
function saveModel(final=false, redir=null) {
	if (final) {
		if ($('#saveModelDialog').length == 0) {
			var md = $('<div id="saveModelDialog" style="display:none;"></div>');
			md.appendTo('body');
		}
		if (saveModelDialogHtml == null) {
			$.ajax({
				url: 'ajax_modelo.php',
				type: 'get',
				dataType: 'html',
				data: { m: 'getSaveModelDialogHtml', file: modelFile },
				success: function(html) {
					saveModelDialogHtml = html;
					saveModel(final, redir);
				}
			});
			return;
		}
		$('#saveModelDialog').html(saveModelDialogHtml);
		fillSaveModelDialog(redir);
		$('#saveModelDialog').dialog({
			modal: true,
			width: 700,
			position: {
				my: 'center top',
				at: 'center top+80'
			},
			closeText: 'Fechar',
			title: 'Salvar modelo',
			dialogClass: 'default',
			buttons: [],
			close: function() {
				$('#saveModelDialog').dialog('close').remove();
			}
		});
	}else{
		$.ajax({
			url: 'ajax_modelo.php',
			type: 'POST',
			data: {
				m: 'saveModel',
				file: modelFile,
				tbDefs: JSON.stringify({'entities':tbDefs, 'relationships':relats}),
				modelDefs: JSON.stringify(modelDefs),
				final: false
			},
			success: function(response) {
				// eval(response); // Execute the response (likely an alert)
				console.log(response);
			},
			error: function() {
				alert('Error saving model');
			}
		});
	}
}
function gera(){
	saveModel(true, 'gera');
}
function fillSaveModelDialog(redir) {
	var txtRedir='';
	if (!redir) redir='';
	if (redir=='gera') {
		txtRedir='Uma nova página será aberta para o GeraClasses';
		redir="'gera'";
	}
	$('#saveModelDialog').html($('#saveModelDialog').html().replace(/__txtRedir__/g, txtRedir));
	$('#saveModelDialog').html($('#saveModelDialog').html().replace(/__redir__/g, redir));
	var html=$('#saveModelDialog').html();
	// debugger;
	html=html.replace(/__modelSuffix__/g, (modelDefs?.modelSuffix?modelDefs.modelSuffix:''));
	html=html.replace(/__modelSchema__/g, (modelDefs?.modelSchema?modelDefs.modelSchema:''));
	$('#saveModelDialog').html(html);
}
function doSaveModel(redir='') {
	modelDefs.modelSuffix=$('#saveModelSuffix').val();
	modelDefs.modelSchema=$('#saveModelSchema').val();
	$.ajax({
		url: 'ajax_modelo.php',
		type: 'POST',
		data: {
			m: 'saveModel',
			file: modelFile,
			tbDefs: JSON.stringify({'entities':tbDefs, 'relationships':relats}),
			modelDefs: JSON.stringify(modelDefs),
			final: true,
			redir: redir
		},
		success: function(response) {
			// eval(response); // Execute the response (likely an alert)
			console.log(response);
			$('#saveModelDialog').dialog('close').remove();

			// Mark model as saved
			markModelSaved();
		},
		error: function() {
			alert('Error saving model');
		}
	});
}
function removeTable(tableId){
	if (!confirm('Tem certeza que deseja remover a tabela '+tableId+'?')) return;
	// remove all relationships that have this table as source or target
	var msgs=[];
	var colsToRemove=[];
	for (var relId in relats){
		if (relats[relId].sourceTable==tableId){ // if this is the source table, remove fkref from target columns, adjust fk attribute if necessary and add message to alert of remaining columns
			for (var i=0; i<relats[relId].columns.length; i++){
				var colDef = tbDefs[relats[relId].targetTable].columns[relats[relId].columns[i].child];
				var idx = colDef.fkrefs.indexOf(relId);
				if (idx!=-1) {
					colDef.fkrefs.splice(idx, 1);
					// if there are no more fkrefs on the table that points to this column as a child, remove the fk attribute of the column
					if (colDef.fkrefs.length==0) {
						delete colDef.fk;
						msgs.push('A coluna '+colDef.logical+' da tabela '+relats[relId].targetTable+' não possui mais FKs. Verifique se é necessário manter a coluna.');
						colsToRemove.push({table: relats[relId].targetTable, column: relats[relId].columns[i].child});
					}
				}
				// remove relId from the list of parents of the target table
				var idx = tbDefs[relats[relId].targetTable].parents.indexOf(relId);
				if (idx!=-1) tbDefs[relats[relId].targetTable].parents.splice(idx, 1);
			}
			delete relats[relId];
		}else if (relats[relId].targetTable==tableId){
			// remove relId from the list of children of the source table
			var idx = tbDefs[relats[relId].sourceTable].children.indexOf(relId);
			if (idx!=-1) tbDefs[relats[relId].sourceTable].children.splice(idx, 1);
			delete relats[relId];
		}
	}
	if (msgs.length>0) {
		// alert(msgs.join('\n'));
		for(var i=0; i<colsToRemove.length; i++){
			if (confirm('Deseja remover a coluna '+colsToRemove[i].column+' da tabela '+colsToRemove[i].table+'?')) {
				delete tbDefs[colsToRemove[i].table].columns[colsToRemove[i].column];
			}
		}
	}
	$('#msbContent #'+tableId+'_logical').remove();
	$('#msbContent #'+tableId+'_physical').remove();
	delete tbDefs[tableId];
	delete tbPositions[tableId];
	delete mmTbs[tableId];
	$('#editTableDialog').dialog('close').remove();
	$('#'+tableId).remove();
	$('#'+tableId+'_mm').remove();
	drawTables();
	drawFkLines();
}
function addTableToSidebar(tableId){
	var logicalName = tbDefs[tableId].logical;
	if ($('#msbContent #'+tableId+'_logical').length>0) return;
	if (showing['list']=='physical') {
		stl='style="display:none;"';
		stp='style="display:block;"';
	} else {
		stl='style="display:block;"';
		stp='style="display:none;"';
	}
	var logicalDiv=$('<div ondblclick="editTable(\''+tableId+'\')" onclick="selLocateTable(\''+tableId+'\')" class="tablebox logical" id="'+tableId+'_logical" title="'+tableId+'" '+stl+'>'+logicalName+'</div>');
	var inserted = false;
	var last=null;
	$("#msbContent div.logical").each(function() {
		if (logicalName < $(this).html()) {
			logicalDiv.insertBefore($(this));
			inserted = true;
			return false; // Exit the loop
		}
		last=$(this);
	});
	if (!inserted) {
		if (last) logicalDiv.insertAfter(last); else $('#msbContent').prepend(logicalDiv);
	}
	var physicalDiv=$('<div ondblclick="editTable(\''+tableId+'\')" onclick="selLocateTable(\''+tableId+'\')" class="tablebox physical" id="'+tableId+'_physical" title="'+logicalName+'" '+stp+'>'+tableId+'</div>');
	inserted = false;
	last=null;
	$("#msbContent div.physical").each(function() {
		if (tableId.toUpperCase() < $(this).attr("id").toUpperCase()) {
			physicalDiv.insertBefore($(this));
			inserted = true;
			return false; // Exit the loop
		}
		last=$(this);
	});
	if (!inserted) {
		if (last) physicalDiv.insertAfter(last); else $('#msbContent').prepend(physicalDiv);
	}
}
function newTable(){
	creatingTable=true;
	$('#divModelo').removeClass('relChild');
	$('#divModelo').removeClass('relParent');
	$('#divModelo').addClass('newTable');
}
function doNewTable(e){
	creatingTable=false;
	// Prevent default behavior
	e.preventDefault();
	var containerOffset = $(modelContainer).offset();
	var tableX = Math.floor((e.pageX - containerOffset.left)/currentScale);
	var tableY = Math.floor((e.pageY - containerOffset.top)/currentScale);
	var tb={
		"logical": 'NOVA TABELA',
		"columns": {},
		"constraints": {},
		"indexes": {},
		"position": [tableX, tableY]
	}
	if (!tbDefs['newTable']) {
		tbDefs['newTable']=tb;
		tbPositions['newTable']=[tableX, tableY];
		// drawTables();
		drawTable('newTable', tb);
	}
	addTableToSidebar('newTable');
	if ($('#editTableDialog').length==0){
		var md=$('<div id="editTableDialog" style="display:none;"></div>');
		md.appendTo('body');
	}
	if (editTableHtml==null){
		$.ajax({
			url:'ajax_modelo.php',
			type:'get',
			dataType:'html',
			data:{m:'editTable', file:modelFile},
			success:function(html){
				editTableHtml=html;
				doNewTable(e);
			}
		});
		return;
	}
	$('#editTableDialog').html(editTableHtml);
	fillNewTableDialog();
	$('#editTableDialog').dialog({
		modal:true,
		width:700,
		height:'auto',
		position: {
			my: 'right top',
			at: 'right-40 top+80'
		},
		closeText:'Fechar',
		title: 'Nova tabela',
		dialogClass: 'default',
		buttons: [],
		close: function() {
			$('#editTableDialog').dialog('close').remove();
		}
	});

}
function fillNewTableDialog(){
	var html=$('#editTableDialog').html();
	html=html.replace(/'__tableId__'/g, '\'newTable\'');
	html=html.replace(/__tableId__/g, '');
	html=html.replace(/__logicalName__/g, '');
	html=html.replace(/__numUniques__/g, 0);
	$('#editTableDialog').html(html);
	// var tr=$('#templateColumnTr').clone();
	$('#templateColumnTr').hide();
}
function abreGera(){
	abrePosta('/gera/index.php', {file: modelFile});
}
function abrePosta(url, data){
	var oForm=document.getElementById('formPosta');
	if (!oForm) {
		oForm=document.createElement('form');
		oForm.id='formPosta';
		oForm.style.display='none';
		document.body.appendChild(oForm);
	}
	oForm.action=url;
	oForm.method='post';
	oForm.target='_blank';
	for (var key in data){
		var input=document.createElement('input');
		input.type='hidden';
		input.name=key;
		input.value=data[key];
		oForm.appendChild(input);
	}
	oForm.submit();
}