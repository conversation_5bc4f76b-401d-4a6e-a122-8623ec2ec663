html,body{
	height: 100%;
	overflow: hidden;
}
body{
	margin: 0;
	padding: 0;
	font-size: 13px;
}
body.claro{
	background-color: #ffffff;
	color: #000000;
}
body.escuro{
	background-color: #333333;
	color: #eeeeee;
}
[data-color-scheme="dark"] {
	color-scheme: dark;
}
[data-color-scheme="light"] {
	color-scheme: light;
}
.form-group-sm {
	margin-bottom: 8px;
}
#mapWrapper{
	width:100%;
	height: 96%;
	position:relative;
	/* font-family: Arial, Helvetica, sans-serif; */
}
#modelo-wrapper{
	position: absolute;
	top: 25px;
	left: 200px;
	width: calc(100% - 200px);
	height: calc(100% - 21px);
	overflow: hidden;
}
.claro #modelo-wrapper{
	background-color: #ffffff;
}
.escuro #modelo-wrapper{
	background-color: #333333;
}
#divModelo{
	position:relative;
	overflow: hidden;
}
.claro #divModelo{
	background-color: #eeeeee;
}
.escuro #divModelo{
	background-color: #333333;
}
#divModelo.relParent{
	cursor: url('/modela/images/cursor-parent-16.png'), auto;
}
#divModelo.relChild{
	cursor: url('/modela/images/cursor-child-16.png'), auto;
}
#divModelo.newTable{
	cursor: url('/modela/images/cursor-table.png'), auto;
}
#maptools{
	position:relative;
	z-index:10000;
	width: 100%;
	height: 25px;
	padding: 2px;
	border-bottom: solid 1px #c7c7c7;
	background-color: #ffffff;
}
.escuro #maptools{
	background-color: #000000;
	border-bottom: solid 1px #555555;
}
#status-footer{
	position:absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 21px;
	padding:1px;
	border-top: solid 1px #c7c7c7;
}
.escuro #status-footer{
	background-color: #000000;
	border-top: solid 1px #555555;
}
#infofile{
	position:absolute;
	top: 0;
	left: 0;
	min-width:200px;
	padding: 2px;
}
#infozoom{
	border-left:solid 1px lightgray;
	min-width:100px;
	float:right;
	clear:left;
	text-align: center;
	padding: 2px;
}
#infopos{
	border-left:solid 1px lightgray;
	min-width:140px;
	float:right;
	clear:left;
	text-align: center;
	padding: 2px;
}
#infotable{
	border-left:solid 1px lightgray;
	min-width:200px;
	float:right;
	clear:left;
	padding: 2px;
}
#infostatus{
	position:absolute;
	left: 210px;
	top: 30px;
	border:solid 1px black;
	min-width:200px;
	min-height: 20px;
	text-align: center;
	padding: 2px;
	font-size: 1.2em;
	z-index: 10000;
	display: none;
}
.change-indicator{
	color: red;
	font-weight: bold;
	/* font-size: 1.2em; */
	display: none;
}
.escuro .change-indicator{
	color: #ff9900;
}
.mapfull{
	width:100%;
	height: 96%;
}
.sttable{
	padding: 0;
	z-index: 1000;
	display:inline-block;
	position: absolute;
}
.claro .sttable{
	border: dotted 1px gray;
	background-color: #fcfcfc;
}
.escuro .sttable{
	border: dotted 1px #dddddd;
	background-color: #333333;
}
.relParent .sttable{
	cursor: url('/modela/images/cursor-parent-16.png'), auto!important;
}
.relChild .sttable{
	cursor: url('/modela/images/cursor-child-16.png'), auto!important;
}
.claro .sttable.selected{
	border: solid 2px black;
	background-color: #ffffff;
}
.escuro .sttable.selected{
	border: solid 2px #ffffff;
	background-color: #666666;
}
.tbName{
	font-weight: bold;
	font-size: 1.2em;
	padding: 2px 3px;
	border-bottom: dotted 1px gray;
}
.selected .tbName{
	border-bottom: solid 1px black!important;
}
.stcolumn{
	font-size: 0.9em;
	padding: 0 2px;
	border-bottom: dotted 1px gray;
	font-weight: bold;
}
.stcolumn .physical{
	font-size:1em;
}
.simulink {
	cursor: pointer;
	color: #0066cc;
}
.simulink:hover {
	text-decoration: underline;
}
.escuro .simulink {
	color: #1188dd;
}
.escuro a{
	color: #1188dd;
}
.escuro .table-striped > tbody > tr:nth-of-type(odd) {
	background-color: #444444;
}
.claro .fk{
	background-color: #ffffcc!important;
}
.claro .pk{
	background-color: #ddddff!important;
}
.claro .fk.pk{
	background-color: #bbdddd!important;
}
.escuro .fk{
	background-color: #ff6f10!important;
}
.escuro .pk{
	background-color: #6655aa!important;
}
.escuro .fk.pk{
	background-color: #117733!important;
}
.stcolumn.pk{
	border-bottom: solid 0px black!important;
}
.nullable{
	font-weight: normal!important;
}
.unique{
	display:inline-block;
	/* border: solid 1px blue;
	border-radius: 50%;
	width: 12px;
	height: 12px; */
	text-align: center;
	font-size: 1em;
	font-weight: bold;
	margin-left: 2px;
	color: red!important;
}
.stpks{
	border-bottom: solid 1px black;
}
#modelSideBar{
	position: absolute;
	top: 25px;
	left: 0;
	width: 200px;
	height: calc(100% - 21px);
	overflow: hidden;
	z-index: 999;
	background-color: #ffffff;
	border-right: solid 1px #c7c7c7;
}
.claro #modelSideBar{
}
.escuro #modelSideBar{
	background-color: #333333;
	border-right: solid 1px #555555;
}
#msbHeader{
	position:relative;
	height: 21px;
	background-color: #f3f3f3;
	border-bottom: solid 1px #c7c7c7;
	text-align: center;
	font-size: 1.1em;
	font-weight: bold;
	padding: 2px;
}
.escuro #msbHeader{
	background-color: #333333;
	color: #ffffff;
	border-bottom: solid 1px #444444;
}
#msbContent{
	overflow: auto;
	height: calc(100% - 21px);
}
.tablebox{
	padding: 1px 3px;
	border-bottom: solid 1px #eeeeee;
	cursor: pointer;
	font-size: 0.9em;
}
.tablebox.logical{
	font-size:0.8em;
}
.tablebox.selected{
	font-weight: bold;
}
.claro .tablebox.selected{
	background-color: #eeeeee;
	border-bottom: solid 1px gray!important;
	border-top: solid 1px gray!important;
}
.escuro .tablebox.selected{
	background-color: #888888;
	color: #ffffff;
	border-bottom: solid 1px lightgray!important;
	/* border-top: solid 1px lightgray!important; */
}
.logical{
	display: none;
}
.physical{
	display: block;
	white-space: nowrap;
}
#minimap-wrapper{
	position: absolute;
	top: 25px;
	right: 2px;
	width: 200px;
	height: 200px;
	overflow: hidden;
	background-color: #ffffff;
	z-index: 99;
	border: solid 2px gray;
}
#minimap{
	position: absolute;
	width: 100%;
	overflow: hidden;
}
.claro #minimap{
	background-color: #ffffff;
}
.escuro #minimap{
	background-color: #666666;
}
#minimap-viewport{
	position: absolute;
	box-sizing: border-box;
}
.claro #minimap-viewport{
	border: solid 2px #0000ff77;
}
.escuro #minimap-viewport{
	border: solid 2px #ddddff88;
}
.mmtable{
	position: absolute;
	border: solid 1px gray;
	background-color: #eeeeee;
}
.mmtable.selected{
	border: solid 1px black;
	background-color: #0000ff;
}
.claro .mmtable{
	border: solid 1px gray;
	background-color: #eeeeee;
}
.escuro .mmtable{
	border: solid 1px #aaaaaa;
	background-color: #888888;
}
.claro .mmtable.selected{
	border: solid 1px black;
	background-color: #0000ff;
}
.escuro .mmtable.selected{
	border: solid 1px #ccccff;
	background-color: #ffffff;
}
#conteudo{
	width: 100%;
	height: 100%;
	overflow: hidden;
}
#tooltipModelo{
	position: absolute;
	padding: 2px;
	font-size: 0.8em;
	z-index: 10000;
	display: none;
}
.claro #tooltipModelo{
	background-color: #ffffff;
	border: solid 1px gray;
}
.escuro #tooltipModelo{
	background-color: #444444;
	border: solid 1px #aaaaaa;
}
.titleTooltip{
	background-color:whitesmoke;
	font-weight:bold;
	color:navy;
}
.escuro .titleTooltip{
	background-color:#666666;
	color:#ffffff;
}
label{
	margin-bottom: 0;
}
.escuro .btn-default{
	background-color: #555555;
	border-color: #777777;
	color: #eeeeee;
}
.escuro .ui-dialog {
	background-color: #fff;
	color: #000;
}
.escuro .ui-dialog-titlebar {
	background-color: #eee;
	color: #000;
}
.escuro .ui-widget-overlay {
	background-color: rgba(0, 0, 0, 0.4);
}

.escuro .ui-dialog {
	background-color: #333;
	color: #fff;
}
.escuro .ui-dialog-titlebar {
	background-color: #555;
	color: #fff;
}
.escuro .ui-dialog-buttonpane {
	background-color: #555;
	color: #fff;
}
.escuro .ui-widget-overlay {
	background-color: rgba(0, 0, 0, 0.8);
}
.escuro .form-control{
	background-color: #444444;
	border-color: #666666;
	color: #ffffff;
}
.escuro .ui-widget-content{
	color: #ffffff;
}
.escuro .ui-button{
	color: #ffffff;
	background-color: #555555;
}
.claro .grayed-bg{
	background-color: whitesmoke;
}
.escuro .grayed-bg{
	background-color: #666666;
}
.sucesso, .claro .sucesso{
	background-color: lightgreen;
}
.escuro .sucesso{
	background-color: #336633;
}
.erro, .claro .erro{
	background-color: red;
}
.escuro .erro{
	background-color: #aa2222;
}